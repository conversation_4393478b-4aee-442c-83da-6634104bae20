version: 2
jobs:
  build:
    docker:
      - image: nvidia/cuda:11.6.1-cudnn8-devel-ubuntu20.04
    steps:
      - checkout
      - run:
          name: Install build tools
          command: |
            apt-get update
            apt-get -y install git python3-pip gcc-10 g++-10 clang-12 zlib1g zlib1g-dev
            pip3 install meson==0.63
            pip3 install ninja
      - run:
          name: "Pull Submodules"
          command: git submodule update --init
      - run:
          name: Meson GCC
          environment:
            CC: gcc-10
            CXX: g++-10
          command: meson build-gcc -Dgtest=false
      - run:
          name: Meson Clang
          environment:
            CC: clang-12
            CXX: clang++-12
          command: meson build-clang -Dgtest=false -Db_lto=false
      - run:
          name: Build GCC
          command: |
            cd build-gcc
            ninja -j 4
      - run:
          name: Build Clang
          command: |
            cd build-clang
            ninja -j 4
  "mac":
    macos:
      xcode: 14.1.0
    resource_class: macos.m1.medium.gen1
    steps:
      - checkout
      - run:
          name: "Pull Submodules"
          command: git submodule update --init
      - run:
          name: Install build tools
          command: |
            pip3 install meson==1.3.0
            pip3 install ninja
            curl -LJOs https://github.com/ispc/ispc/releases/download/v1.21.0/ispc-v1.21.0-macOS.universal.tar.gz
            tar xf ispc-v1.21.0-macOS.universal.tar.gz
            cp ispc-v1.21.0-macOS.universal/bin/ispc /usr/local/bin
      - run:
          name: Build lc0
          command: |
            meson build --buildtype=release -Dgtest=false -Dopencl=false  --cross-file cross-files/x86_64-darwin
            cd build
            ninja
      - run:
          name: Build lc0 arm
          command: |
            meson build-arm --buildtype=release -Dgtest=false -Dopencl=false
            cd build-arm
            ninja
      - run:
          name: Make universal binary
          command: lipo -create -o /tmp/lc0 build/lc0 build-arm/lc0
      - store_artifacts:
          path: /tmp/lc0
          destination: lc0-macos_12.6.1
workflows:
  version: 2
  builds:
    jobs:
      - build
      - "mac"
