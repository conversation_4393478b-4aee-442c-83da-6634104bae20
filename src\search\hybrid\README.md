# Hybrid Search Algorithm

This directory contains an experimental hybrid search algorithm that combines traditional alpha-beta pruning with Monte Carlo Tree Search (MCTS), similar to how Stockfish uses alpha-beta search.

## Overview

The hybrid search algorithm operates in two phases:

1. **Alpha-Beta Phase**: For shallow depths (configurable, default 5 plies), the engine uses traditional alpha-beta pruning with:
   - Neural network evaluation at leaf nodes
   - Transposition table for caching positions
   - Move ordering based on neural network policy and heuristics
   - Beta cutoffs for pruning bad branches

2. **MCTS Phase**: After the alpha-beta phase, the engine can optionally continue with MCTS iterations to refine the evaluation.

## Key Features

- **Neural Network Integration**: Uses Lc0's neural networks for both position evaluation and move ordering
- **Transposition Table**: Efficient position caching with proper bound types (exact, lower, upper)
- **Advanced Move Ordering**: Combines neural network policy with classical heuristics:
  - Policy values from neural network
  - Capture moves prioritized
  - Checking moves boosted
  - Transposition table best moves tried first
- **Time Management**: Supports all standard UCI time controls
- **Pondering**: Can continue searching while opponent thinks

## Configuration Options

- `HybridAlphaBetaDepth` (default: 5): Maximum depth for alpha-beta search
- `HybridMCTSIterations` (default: 800): Number of MCTS iterations after alpha-beta
- `HybridUseTranspositionTable` (default: true): Enable/disable transposition table
- `HybridTTSizeMB` (default: 128): Transposition table size in megabytes
- `HybridContempt` (default: 0): Contempt factor for draw evaluation

## Usage

To use the hybrid search algorithm, set the search type in UCI:

```
setoption name SearchType value hybrid
```

## Implementation Details

### Files

- `search.h/cc`: Main search algorithm implementation
- `transposition_table.h/cc`: Transposition table for position caching
- `wrapper.cc`: Registration with Lc0's search factory

### Algorithm Flow

1. **Iterative Deepening**: The search starts at depth 1 and increases until time/depth limits
2. **Root Search**: At the root, all moves are searched with full window
3. **Alpha-Beta Recursion**: Interior nodes use zero-window searches with alpha-beta pruning
4. **Neural Network Calls**: Positions are evaluated using the neural network, with results cached
5. **Transposition Table**: Positions are stored/retrieved to avoid re-searching

### Performance Considerations

- Move ordering is critical for alpha-beta efficiency
- Neural network calls are cached to avoid redundant evaluations
- Transposition table uses clustered entries for better cache performance
- Thread-safe implementation using spinlocks for minimal overhead

## Future Improvements

- Implement MCTS phase after alpha-beta
- Add null move pruning
- Implement late move reductions (LMR)
- Add aspiration windows
- Integrate with existing MCTS tree for hybrid approach
- Add killer moves and history heuristics