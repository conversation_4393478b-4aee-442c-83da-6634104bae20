/*
  This file is part of Leela Chess Zero.
  Copyright (C) 2024 The LCZero Authors

  Leela Chess is free software: you can redistribute it and/or modify
  it under the terms of the GNU General Public License as published by
  the Free Software Foundation, either version 3 of the License, or
  (at your option) any later version.

  Leela Chess is distributed in the hope that it will be useful,
  but WITHOUT ANY WARRANTY; without even the implied warranty of
  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
  GNU General Public License for more details.

  You should have received a copy of the GNU General Public License
  along with Lee<PERSON> Chess.  If not, see <http://www.gnu.org/licenses/>.
*/

#include "search/hybrid/search.h"

#include <algorithm>
#include <cmath>
#include <limits>

#include "chess/bitboard.h"
#include "chess/uciloop.h"
#include "neural/encoder.h"
#include "neural/network.h"
#include "search/hybrid/transposition_table.h"
#include "utils/logging.h"
#include "utils/optionsparser.h"
#include "utils/random.h"

namespace lczero {
namespace hybrid {

namespace {
// Constants for search
constexpr float kInfinity = std::numeric_limits<float>::max();
constexpr int kMaxPly = 128;
constexpr int kDefaultAlphaBetaDepth = 5;
constexpr int kDefaultMCTSIterations = 800;

// Option IDs
const OptionId kHybridAlphaBetaDepthId{
    "HybridAlphaBetaDepth", "HybridAlphaBetaDepth",
    "Maximum depth for alpha-beta search phase."};
const OptionId kHybridMCTSIterationsId{
    "HybridMCTSIterations", "HybridMCTSIterations",
    "Number of MCTS iterations after alpha-beta."};
const OptionId kHybridUseTranspositionTableId{
    "HybridUseTranspositionTable", "HybridUseTranspositionTable",
    "Use transposition table in alpha-beta search."};
const OptionId kHybridTTSizeMBId{
    "HybridTTSizeMB", "HybridTTSizeMB",
    "Transposition table size in MB."};
const OptionId kHybridContemptId{
    "HybridContempt", "HybridContempt",
    "Contempt factor (positive favors avoiding draws)."};

// Convert win probability to centipawns
float WinProbToCentipawns(float win_prob) {
  if (win_prob >= 1.0f) return 10000.0f;
  if (win_prob <= 0.0f) return -10000.0f;
  return 400.0f * std::log10(win_prob / (1.0f - win_prob));
}

// Convert centipawns to win probability
float CentipawnsToWinProb(float cp) {
  return 1.0f / (1.0f + std::pow(10.0f, -cp / 400.0f));
}
}  // namespace

HybridSearch::HybridSearch(UciResponder* responder, const OptionsDict* options)
    : SearchBase(responder), options_(options) {
  // Load configuration
  alpha_beta_depth_ = options->Get<int>(kHybridAlphaBetaDepthId);
  mcts_iterations_ = options->Get<int>(kHybridMCTSIterationsId);
  use_transposition_table_ = options->Get<bool>(kHybridUseTranspositionTableId);
  contempt_ = options->Get<float>(kHybridContemptId);
  
  // Initialize transposition table if enabled
  if (use_transposition_table_) {
    tt_ = std::make_unique<TranspositionTable>(options->Get<int>(kHybridTTSizeMBId));
  }
}

HybridSearch::~HybridSearch() {
  AbortSearch();
  for (auto& thread : search_threads_) {
    if (thread.joinable()) {
      thread.join();
    }
  }
}

void HybridSearch::NewGame() {
  nodes_searched_ = 0;
  nn_cache_.clear();
  if (tt_) {
    tt_->Clear();
  }
}

void HybridSearch::SetPosition(const GameState& state) {
  current_position_ = state;
}

void HybridSearch::StartSearch(const GoParams& params) {
  // Reset search state
  stop_ = false;
  nodes_searched_ = 0;
  current_depth_ = 0;
  best_move_ = Move();
  ponder_move_ = Move();
  best_score_ = 0.0f;
  
  // Parse search limits
  if (params.wtime || params.btime) {
    // Calculate time for this move
    auto our_time = current_position_.GetPosition().IsWhiteToMove() ? params.wtime : params.btime;
    auto our_inc = current_position_.GetPosition().IsWhiteToMove() ? params.winc : params.binc;
    if (our_time) {
      // Simple time management: use 2% of remaining time + 80% of increment
      auto time_for_move = (*our_time) / 50;
      if (our_inc) {
        time_for_move += (*our_inc) * 4 / 5;
      }
      time_limit_ = std::chrono::milliseconds(time_for_move);
    }
  }
  
  if (params.movetime) {
    time_limit_ = std::chrono::milliseconds(*params.movetime);
  }
  
  if (params.nodes) {
    nodes_limit_ = *params.nodes;
  }
  
  if (params.depth) {
    depth_limit_ = *params.depth;
  }
  
  pondering_ = params.ponder;
  
  // Start worker thread
  search_threads_.emplace_back(&HybridSearch::SearchThread, this);
}

void HybridSearch::StartClock() {
  start_time_ = std::chrono::steady_clock::now();
}

void HybridSearch::WaitSearch() {
  for (auto& thread : search_threads_) {
    if (thread.joinable()) {
      thread.join();
    }
  }
  search_threads_.clear();
}

void HybridSearch::StopSearch() {
  stop_ = true;
  WaitSearch();
  
  // Send best move
  if (best_move_ != Move()) {
    if (ponder_move_ != Move()) {
      uci_responder_->OutputBestMove(best_move_, ponder_move_);
    } else {
      uci_responder_->OutputBestMove(best_move_);
    }
  }
}

void HybridSearch::AbortSearch() {
  stop_ = true;
  WaitSearch();
}

void HybridSearch::SearchThread() {
  const Position& root_pos = current_position_.GetPosition();
  
  // Iterative deepening with alpha-beta
  for (int depth = 1; depth <= alpha_beta_depth_ && !ShouldStop(); ++depth) {
    current_depth_ = depth;
    
    // Search at current depth
    auto result = SearchRoot(root_pos, depth);
    
    if (!ShouldStop()) {
      best_move_ = result.move;
      best_score_ = result.score;
      
      // Extract PV for info
      std::vector<Move> pv;
      pv.push_back(best_move_);
      
      SendInfo(depth, best_score_, pv);
    }
  }
  
  // After alpha-beta, optionally run MCTS for deeper analysis
  if (!ShouldStop() && mcts_iterations_ > 0) {
    RunMCTS(root_pos, mcts_iterations_);
  }
  
  // If pondering, continue until stopped
  while (pondering_ && !stop_) {
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
  }
}

ScoredMove HybridSearch::SearchRoot(const Position& pos, int depth) {
  auto moves = GenerateMoves(pos);
  auto ordered_moves = OrderMoves(pos, moves);
  
  float alpha = -kInfinity;
  float beta = kInfinity;
  ScoredMove best_result{Move(), -kInfinity, 0.0f, 0};
  
  for (const auto& move_info : ordered_moves) {
    if (ShouldStop()) break;
    
    // Make move
    Position new_pos = pos;
    new_pos.ApplyMove(move_info.move);
    
    // Search with negamax
    std::vector<Move> pv;
    float score = -AlphaBeta(new_pos, -beta, -alpha, depth - 1, 1, &pv);
    
    if (score > best_result.score) {
      best_result.move = move_info.move;
      best_result.score = score;
      best_result.policy = move_info.policy;
      best_result.depth = depth;
      
      // Update ponder move from PV
      if (!pv.empty()) {
        ponder_move_ = pv[0];
      }
    }
    
    alpha = std::max(alpha, score);
    
    // Beta cutoff (not useful at root, but kept for consistency)
    if (alpha >= beta) {
      break;
    }
  }
  
  return best_result;
}

float HybridSearch::AlphaBeta(const Position& pos, float alpha, float beta,
                              int depth, int ply, std::vector<Move>* pv) {
  nodes_searched_++;
  
  // Check limits
  if (ShouldStop()) {
    return 0.0f;
  }
  
  // Terminal node checks
  if (pos.IsGameOver()) {
    GameResult result = pos.GetGameResult();
    if (result == GameResult::DRAW) {
      return contempt_ * (pos.IsWhiteToMove() ? 1.0f : -1.0f);
    } else if (result == GameResult::WHITE_WON) {
      return pos.IsWhiteToMove() ? kInfinity - ply : -kInfinity + ply;
    } else {
      return pos.IsWhiteToMove() ? -kInfinity + ply : kInfinity - ply;
    }
  }
  
  // Depth limit - evaluate position
  if (depth <= 0 || ply >= kMaxPly) {
    return EvaluatePosition(pos);
  }
  
  // Transposition table lookup
  if (tt_) {
    const TTEntry* tt_entry = tt_->Probe(pos.Hash());
    if (tt_entry && tt_entry->depth >= depth) {
      float tt_score = TranspositionTable::ScoreFromTT(tt_entry->score, ply);
      
      // Check bound type
      if (tt_entry->bound == BoundType::EXACT) {
        // Exact score - we can return immediately
        if (pv && tt_entry->best_move != Move()) {
          pv->push_back(tt_entry->best_move);
        }
        return tt_score;
      } else if (tt_entry->bound == BoundType::LOWER && tt_score >= beta) {
        // Beta cutoff
        return tt_score;
      } else if (tt_entry->bound == BoundType::UPPER && tt_score <= alpha) {
        // Alpha cutoff
        return tt_score;
      }
    }
  }
  
  // Generate and order moves
  auto moves = GenerateMoves(pos);
  if (moves.empty()) {
    // No legal moves - either checkmate or stalemate
    if (pos.IsInCheck()) {
      return -kInfinity + ply;  // Checkmate
    } else {
      return contempt_ * (pos.IsWhiteToMove() ? 1.0f : -1.0f);  // Stalemate
    }
  }
  
  auto ordered_moves = OrderMoves(pos, moves);
  
  // Search moves
  float best_score = -kInfinity;
  std::vector<Move> best_pv;
  
  for (const auto& move_info : ordered_moves) {
    if (ShouldStop()) break;
    
    // Make move
    Position new_pos = pos;
    new_pos.ApplyMove(move_info.move);
    
    // Recursive search
    std::vector<Move> child_pv;
    float score = -AlphaBeta(new_pos, -beta, -alpha, depth - 1, ply + 1, &child_pv);
    
    if (score > best_score) {
      best_score = score;
      best_pv.clear();
      best_pv.push_back(move_info.move);
      best_pv.insert(best_pv.end(), child_pv.begin(), child_pv.end());
    }
    
    alpha = std::max(alpha, score);
    
    // Beta cutoff
    if (alpha >= beta) {
      // TODO: Store in TT as beta cutoff
      break;
    }
  }
  
  // Store in transposition table
  if (tt_) {
    BoundType bound_type = BoundType::EXACT;
    if (best_score >= beta) {
      bound_type = BoundType::LOWER;  // Beta cutoff
    } else if (best_score <= alpha) {
      bound_type = BoundType::UPPER;  // No improvement
    }
    
    Move best_tt_move = best_pv.empty() ? Move() : best_pv[0];
    tt_->Store(pos.Hash(), best_score, EvaluatePosition(pos), 
               best_tt_move, depth, bound_type);
  }
  
  // Return PV
  if (pv && !best_pv.empty()) {
    *pv = std::move(best_pv);
  }
  
  return best_score;
}

float HybridSearch::EvaluatePosition(const Position& pos) {
  // Check cache first
  uint64_t hash = pos.Hash();
  {
    std::lock_guard<std::mutex> lock(cache_mutex_);
    auto it = nn_cache_.find(hash);
    if (it != nn_cache_.end()) {
      return it->second.value;
    }
  }
  
  // Prepare input for neural network
  InputPlanes planes;
  EncodePosition(pos, &planes);
  
  // Run neural network
  auto input = std::make_unique<InputPlanes>();
  *input = planes;
  
  auto result = backend_->RunBlocking({std::move(input)});
  if (result.empty()) {
    return 0.0f;
  }
  
  // Extract value
  float value = result[0].q;
  
  // Cache result
  {
    std::lock_guard<std::mutex> lock(cache_mutex_);
    nn_cache_[hash] = {hash, value, {}};
  }
  
  // Return from perspective of side to move
  return pos.IsWhiteToMove() ? value : -value;
}

MoveList HybridSearch::GenerateMoves(const Position& pos) {
  return pos.GetLegalMoves();
}

std::vector<ScoredMove> HybridSearch::OrderMoves(const Position& pos,
                                                 const MoveList& moves) {
  std::vector<ScoredMove> scored_moves;
  scored_moves.reserve(moves.size());
  
  // Get policy values from neural network if available
  uint64_t hash = pos.Hash();
  std::vector<float> policy;
  
  {
    std::lock_guard<std::mutex> lock(cache_mutex_);
    auto it = nn_cache_.find(hash);
    if (it != nn_cache_.end() && !it->second.policy.empty()) {
      policy = it->second.policy;
    }
  }
  
  // If no cached policy, run NN to get it
  if (policy.empty()) {
    InputPlanes planes;
    EncodePosition(pos, &planes);
    
    auto input = std::make_unique<InputPlanes>();
    *input = planes;
    
    auto result = backend_->RunBlocking({std::move(input)});
    if (!result.empty() && result[0].policy.size() > 0) {
      // Get policy values from neural network output
      const auto& nn_policy = result[0].policy;
      policy.resize(moves.size());
      
      // Map each move to its policy value
      for (size_t i = 0; i < moves.size(); ++i) {
        // Convert move to policy index
        int policy_idx = pos.GetBoard().GetLegacyMove(moves[i]);
        if (policy_idx >= 0 && policy_idx < static_cast<int>(nn_policy.size())) {
          policy[i] = nn_policy[policy_idx];
        } else {
          policy[i] = 0.0f;  // Invalid move gets zero policy
        }
      }
      
      // Normalize policy values
      float policy_sum = 0.0f;
      for (float p : policy) {
        policy_sum += p;
      }
      if (policy_sum > 0.0f) {
        for (float& p : policy) {
          p /= policy_sum;
        }
      } else {
        // If all policies are zero, use uniform distribution
        for (float& p : policy) {
          p = 1.0f / moves.size();
        }
      }
      
      // Cache policy along with value
      std::lock_guard<std::mutex> lock(cache_mutex_);
      auto& cache_entry = nn_cache_[hash];
      cache_entry.hash = hash;
      cache_entry.value = result[0].q;
      cache_entry.policy = policy;
    }
  }
  
  // Create scored moves
  for (size_t i = 0; i < moves.size(); ++i) {
    float move_policy = (i < policy.size()) ? policy[i] : 1.0f / moves.size();
    scored_moves.push_back({moves[i], 0.0f, move_policy, 0});
  }
  
  // Add additional move ordering heuristics
  for (auto& move_info : scored_moves) {
    float ordering_score = move_info.policy;
    
    // Boost captures
    if (pos.GetBoard().at(move_info.move.to()) != Piece::kEmpty) {
      ordering_score += 0.1f;
    }
    
    // Boost checks
    Position test_pos = pos;
    test_pos.ApplyMove(move_info.move);
    if (test_pos.IsInCheck()) {
      ordering_score += 0.05f;
    }
    
    // Check if move was best in transposition table
    if (tt_) {
      const TTEntry* tt_entry = tt_->Probe(pos.Hash());
      if (tt_entry && tt_entry->best_move == move_info.move) {
        ordering_score += 0.5f;  // Strong boost for TT move
      }
    }
    
    move_info.score = ordering_score;
  }
  
  // Sort by combined ordering score (highest first)
  std::sort(scored_moves.begin(), scored_moves.end(),
            [](const ScoredMove& a, const ScoredMove& b) {
              return a.score > b.score;
            });
  
  return scored_moves;
}

void HybridSearch::RunMCTS(const Position& pos, int iterations) {
  // TODO: Implement MCTS phase
  // This would create a tree starting from the current position
  // and run MCTS simulations to refine the evaluation
  CERR << "MCTS phase not yet implemented";
}

bool HybridSearch::ShouldStop() const {
  if (stop_) return true;
  
  // Check time limit
  if (time_limit_) {
    auto elapsed = std::chrono::steady_clock::now() - start_time_;
    if (elapsed >= *time_limit_) return true;
  }
  
  // Check node limit
  if (nodes_limit_ && nodes_searched_ >= *nodes_limit_) {
    return true;
  }
  
  // Check depth limit
  if (depth_limit_ && current_depth_ >= *depth_limit_) {
    return true;
  }
  
  return false;
}

void HybridSearch::SendInfo(int depth, float score, const std::vector<Move>& pv) {
  auto elapsed = std::chrono::steady_clock::now() - start_time_;
  auto elapsed_ms = std::chrono::duration_cast<std::chrono::milliseconds>(elapsed).count();
  
  // Convert score to centipawns
  int cp_score = static_cast<int>(WinProbToCentipawns(
      (score + 1.0f) / 2.0f));  // Convert [-1, 1] to [0, 1] win probability
  
  // Calculate nps
  int64_t nps = elapsed_ms > 0 ? (nodes_searched_ * 1000) / elapsed_ms : 0;
  
  // Build PV string
  std::string pv_string;
  for (const auto& move : pv) {
    if (!pv_string.empty()) pv_string += " ";
    pv_string += move.ToString();
  }
  
  // Send info
  std::vector<ThinkingInfo> infos;
  infos.emplace_back();
  auto& info = infos.back();
  info.depth = depth;
  info.score = cp_score;
  info.time = elapsed_ms;
  info.nodes = nodes_searched_;
  info.nps = nps;
  info.pv = pv_string;
  
  uci_responder_->OutputThinkingInfo(&infos);
}

// Factory implementation
void HybridSearchFactory::PopulateParams(OptionsParser* parser) const {
  parser->Add<IntOption>(kHybridAlphaBetaDepthId, 1, 20) = kDefaultAlphaBetaDepth;
  parser->Add<IntOption>(kHybridMCTSIterationsId, 0, 100000) = kDefaultMCTSIterations;
  parser->Add<BoolOption>(kHybridUseTranspositionTableId) = true;
  parser->Add<IntOption>(kHybridTTSizeMBId, 1, 32768) = 128;
  parser->Add<FloatOption>(kHybridContemptId, -100.0f, 100.0f) = 0.0f;
}

std::unique_ptr<SearchBase> HybridSearchFactory::CreateSearch(
    UciResponder* responder, const OptionsDict* options) const {
  return std::make_unique<HybridSearch>(responder, options);
}

}  // namespace hybrid
}  // namespace lczero