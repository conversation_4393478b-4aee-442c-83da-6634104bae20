@echo off
setlocal

rem 1. Set the following for the options you want to build.
set CUDNN=true
set CUDA=true
set DX12=false
set OPENCL=false
set MKL=false
set DNNL=false
set OPENBLAS=false
set EIGEN=true
set TEST=false

rem 2. Edit the paths for the build dependencies.
set CUDA_PATH=C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v10.0
set CUDNN_PATH=%CUDA_PATH%
set OPENBLAS_PATH=C:\OpenBLAS
set MKL_PATH=C:\Program Files (x86)\IntelSWTools\compilers_and_libraries\windows\mkl
set DNNL_PATH=C:\dnnl_win_1.1.1_cpu_vcomp
set OPENCL_LIB_PATH=%CUDA_PATH%\lib\x64
set OPENCL_INCLUDE_PATH=%CUDA_PATH%\include

rem Set up Visual Studio environment
if exist "C:\Program Files\Microsoft Visual Studio\2022" (
  where /q cl
  if errorlevel 1 call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvarsall.bat" amd64
  set backend=vs2022
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019" (
  where /q cl
  if errorlevel 1 call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvarsall.bat" amd64
  set backend=vs2019
) else (
  where /q cl
  if errorlevel 1 call "C:\Program Files (x86)\Microsoft Visual Studio\2017\Community\VC\Auxiliary\Build\vcvarsall.bat" amd64
  set backend=vs2017
)

rem Set BLAS option
set BLAS=false
if %OPENBLAS%==true set BLAS=true
if %MKL%==true set BLAS=true
if %DNNL%==true set BLAS=true

rem Set include and lib paths
set CUDNN_INCLUDE_PATH=%CUDNN_PATH%\include
set CUDNN_LIB_PATH=%CUDNN_PATH%\lib\x64

echo Deleting build directory:
if exist build rd /s /q build

echo Setting up meson build...
meson setup build --backend %backend% --buildtype release -Ddx=%DX12% -Dcudnn=%CUDNN% -Dplain_cuda=%CUDA% ^
-Dopencl=%OPENCL% -Dblas=%BLAS% -Dmkl=%MKL% -Dopenblas=%OPENBLAS% -Ddnnl=%DNNL% -Dgtest=%TEST% ^
-Dcudnn_include="%CUDNN_INCLUDE_PATH%" -Dcudnn_libdirs="%CUDNN_LIB_PATH%" ^
-Dmkl_include="%MKL_PATH%\include" -Dmkl_libdirs="%MKL_PATH%\lib\intel64" -Ddnnl_dir="%DNNL_PATH%" ^
-Dopencl_libdirs="%OPENCL_LIB_PATH%" -Dopencl_include="%OPENCL_INCLUDE_PATH%" ^
-Dopenblas_include="%OPENBLAS_PATH%\include" -Dopenblas_libdirs="%OPENBLAS_PATH%\lib" ^
-Ddefault_library=static -Db_vscrt=mt

if errorlevel 1 (
    echo Meson setup failed!
    pause
    exit /b 1
)

echo Build configuration complete. Press any key to start building...
pause

cd build

echo Building with MSBuild...
msbuild /m /p:Configuration=Release /p:Platform=x64 /p:WholeProgramOptimization=true ^
/p:PreferredToolArchitecture=x64 lc0.sln /filelogger

if errorlevel 1 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build completed successfully!
echo Executable is at: build\Release\lc0.exe
pause