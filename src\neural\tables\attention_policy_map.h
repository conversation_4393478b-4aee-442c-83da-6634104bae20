/*
 This file is part of Leela Chess Zero.
 Copyright (C) 2019 The LCZero Authors

 Leela Chess is free software: you can redistribute it and/or modify
 it under the terms of the GNU General Public License as published by
 the Free Software Foundation, either version 3 of the License, or
 (at your option) any later version.

 Leela Chess is distributed in the hope that it will be useful,
 but WITHOUT ANY WARRANTY; without even the implied warranty of
 MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 GNU General Public License for more details.

 You should have received a copy of the GNU General Public License
 along with Lee<PERSON> Chess.  If not, see <http://www.gnu.org/licenses/>.
 */

#pragma once

namespace lczero {

// 64*64 + 8x24
const short kAttnPolicyMap[] = {
    -1,   0,    1,    2,    3,    4,    5,    6,    7,    8,    9,    -1,
    -1,   -1,   -1,   -1,   10,   11,   12,   -1,   -1,   -1,   -1,   -1,
    13,   -1,   -1,   14,   -1,   -1,   -1,   -1,   15,   -1,   -1,   -1,
    16,   -1,   -1,   -1,   17,   -1,   -1,   -1,   -1,   18,   -1,   -1,
    19,   -1,   -1,   -1,   -1,   -1,   20,   -1,   21,   -1,   -1,   -1,
    -1,   -1,   -1,   22,   23,   -1,   24,   25,   26,   27,   28,   29,
    30,   31,   32,   33,   -1,   -1,   -1,   -1,   34,   35,   36,   37,
    -1,   -1,   -1,   -1,   -1,   38,   -1,   -1,   39,   -1,   -1,   -1,
    -1,   40,   -1,   -1,   -1,   41,   -1,   -1,   -1,   42,   -1,   -1,
    -1,   -1,   43,   -1,   -1,   44,   -1,   -1,   -1,   -1,   -1,   45,
    -1,   46,   -1,   -1,   -1,   -1,   -1,   -1,   47,   48,   -1,   49,
    50,   51,   52,   53,   54,   55,   56,   57,   58,   -1,   -1,   -1,
    59,   60,   61,   62,   63,   -1,   -1,   -1,   -1,   -1,   64,   -1,
    -1,   65,   -1,   -1,   -1,   -1,   66,   -1,   -1,   -1,   67,   -1,
    -1,   -1,   68,   -1,   -1,   -1,   -1,   69,   -1,   -1,   70,   -1,
    -1,   -1,   -1,   -1,   -1,   -1,   71,   -1,   -1,   -1,   -1,   -1,
    72,   73,   74,   -1,   75,   76,   77,   78,   -1,   79,   80,   81,
    82,   83,   -1,   -1,   -1,   84,   85,   86,   87,   88,   -1,   -1,
    89,   -1,   -1,   90,   -1,   -1,   91,   -1,   -1,   -1,   -1,   92,
    -1,   -1,   -1,   93,   -1,   -1,   -1,   94,   -1,   -1,   -1,   -1,
    -1,   -1,   -1,   95,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   96,
    -1,   -1,   -1,   -1,   97,   98,   99,   100,  -1,   101,  102,  103,
    -1,   -1,   104,  105,  106,  107,  108,  -1,   -1,   -1,   109,  110,
    111,  112,  113,  -1,   -1,   114,  -1,   -1,   115,  -1,   -1,   116,
    117,  -1,   -1,   -1,   118,  -1,   -1,   -1,   -1,   -1,   -1,   -1,
    119,  -1,   -1,   -1,   -1,   -1,   -1,   -1,   120,  -1,   -1,   -1,
    -1,   -1,   -1,   -1,   121,  -1,   -1,   -1,   122,  123,  124,  125,
    126,  -1,   127,  128,  -1,   -1,   -1,   129,  130,  131,  132,  133,
    -1,   -1,   -1,   134,  135,  136,  137,  138,  -1,   -1,   139,  -1,
    -1,   140,  -1,   -1,   -1,   141,  -1,   -1,   -1,   142,  -1,   -1,
    143,  -1,   -1,   -1,   -1,   144,  -1,   -1,   -1,   -1,   -1,   -1,
    -1,   145,  -1,   -1,   -1,   -1,   -1,   -1,   -1,   146,  -1,   -1,
    147,  148,  149,  150,  151,  152,  -1,   153,  -1,   -1,   -1,   -1,
    154,  155,  156,  157,  -1,   -1,   -1,   -1,   158,  159,  160,  161,
    -1,   -1,   -1,   162,  -1,   -1,   163,  -1,   -1,   -1,   164,  -1,
    -1,   -1,   165,  -1,   -1,   166,  -1,   -1,   -1,   -1,   167,  -1,
    168,  -1,   -1,   -1,   -1,   -1,   169,  -1,   -1,   -1,   -1,   -1,
    -1,   -1,   170,  -1,   171,  172,  173,  174,  175,  176,  177,  -1,
    -1,   -1,   -1,   -1,   -1,   178,  179,  180,  -1,   -1,   -1,   -1,
    -1,   181,  182,  183,  -1,   -1,   -1,   -1,   184,  -1,   -1,   185,
    -1,   -1,   -1,   186,  -1,   -1,   -1,   187,  -1,   -1,   188,  -1,
    -1,   -1,   -1,   189,  -1,   190,  -1,   -1,   -1,   -1,   -1,   191,
    192,  -1,   -1,   -1,   -1,   -1,   -1,   193,  194,  195,  196,  -1,
    -1,   -1,   -1,   -1,   -1,   197,  198,  199,  200,  201,  202,  203,
    204,  205,  206,  -1,   -1,   -1,   -1,   -1,   207,  208,  209,  -1,
    -1,   -1,   -1,   -1,   210,  -1,   -1,   211,  -1,   -1,   -1,   -1,
    212,  -1,   -1,   -1,   213,  -1,   -1,   -1,   214,  -1,   -1,   -1,
    -1,   215,  -1,   -1,   216,  -1,   -1,   -1,   -1,   -1,   217,  -1,
    218,  219,  220,  221,  -1,   -1,   -1,   -1,   222,  -1,   223,  224,
    225,  226,  227,  228,  229,  230,  231,  232,  -1,   -1,   -1,   -1,
    233,  234,  235,  236,  -1,   -1,   -1,   -1,   -1,   237,  -1,   -1,
    238,  -1,   -1,   -1,   -1,   239,  -1,   -1,   -1,   240,  -1,   -1,
    -1,   241,  -1,   -1,   -1,   -1,   242,  -1,   -1,   243,  -1,   -1,
    -1,   -1,   -1,   244,  245,  246,  247,  248,  249,  -1,   -1,   -1,
    250,  251,  -1,   252,  253,  254,  255,  256,  257,  258,  259,  260,
    261,  -1,   -1,   -1,   262,  263,  264,  265,  266,  -1,   -1,   -1,
    -1,   -1,   267,  -1,   -1,   268,  -1,   -1,   -1,   -1,   269,  -1,
    -1,   -1,   270,  -1,   -1,   -1,   271,  -1,   -1,   -1,   -1,   272,
    -1,   -1,   273,  -1,   -1,   -1,   -1,   -1,   -1,   274,  275,  276,
    277,  278,  -1,   -1,   279,  280,  281,  -1,   282,  283,  284,  285,
    -1,   286,  287,  288,  289,  290,  -1,   -1,   -1,   291,  292,  293,
    294,  295,  -1,   -1,   296,  -1,   -1,   297,  -1,   -1,   298,  -1,
    -1,   -1,   -1,   299,  -1,   -1,   -1,   300,  -1,   -1,   -1,   301,
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   302,  -1,   -1,   -1,   -1,
    -1,   -1,   303,  304,  305,  306,  307,  -1,   308,  309,  310,  311,
    -1,   312,  313,  314,  -1,   -1,   315,  316,  317,  318,  319,  -1,
    -1,   -1,   320,  321,  322,  323,  324,  -1,   -1,   325,  -1,   -1,
    326,  -1,   -1,   327,  328,  -1,   -1,   -1,   329,  -1,   -1,   -1,
    -1,   -1,   -1,   -1,   330,  -1,   -1,   -1,   -1,   -1,   -1,   -1,
    331,  -1,   -1,   -1,   -1,   -1,   -1,   332,  333,  334,  335,  336,
    337,  338,  339,  340,  341,  -1,   342,  343,  -1,   -1,   -1,   344,
    345,  346,  347,  348,  -1,   -1,   -1,   349,  350,  351,  352,  353,
    -1,   -1,   354,  -1,   -1,   355,  -1,   -1,   -1,   356,  -1,   -1,
    -1,   357,  -1,   -1,   358,  -1,   -1,   -1,   -1,   359,  -1,   -1,
    -1,   -1,   -1,   -1,   -1,   360,  -1,   -1,   -1,   -1,   -1,   -1,
    361,  362,  363,  364,  365,  366,  367,  368,  369,  370,  -1,   371,
    -1,   -1,   -1,   -1,   372,  373,  374,  375,  -1,   -1,   -1,   -1,
    376,  377,  378,  379,  -1,   -1,   -1,   380,  -1,   -1,   381,  -1,
    -1,   -1,   382,  -1,   -1,   -1,   383,  -1,   -1,   384,  -1,   -1,
    -1,   -1,   385,  -1,   386,  -1,   -1,   -1,   -1,   -1,   387,  -1,
    -1,   -1,   -1,   -1,   -1,   388,  389,  390,  391,  392,  393,  394,
    395,  396,  397,  -1,   -1,   -1,   -1,   -1,   -1,   398,  399,  400,
    -1,   -1,   -1,   -1,   -1,   401,  402,  403,  -1,   -1,   -1,   -1,
    404,  -1,   -1,   405,  -1,   -1,   -1,   406,  -1,   -1,   -1,   407,
    -1,   -1,   408,  -1,   -1,   -1,   -1,   409,  -1,   410,  -1,   -1,
    -1,   -1,   -1,   411,  412,  413,  414,  -1,   -1,   -1,   -1,   -1,
    415,  416,  417,  -1,   -1,   -1,   -1,   -1,   -1,   418,  419,  420,
    421,  422,  423,  424,  425,  426,  427,  -1,   -1,   -1,   -1,   -1,
    428,  429,  430,  -1,   -1,   -1,   -1,   -1,   431,  -1,   -1,   432,
    -1,   -1,   -1,   -1,   433,  -1,   -1,   -1,   434,  -1,   -1,   -1,
    435,  -1,   -1,   -1,   -1,   436,  -1,   -1,   437,  438,  439,  440,
    -1,   -1,   -1,   -1,   441,  442,  443,  444,  -1,   -1,   -1,   -1,
    445,  -1,   446,  447,  448,  449,  450,  451,  452,  453,  454,  455,
    -1,   -1,   -1,   -1,   456,  457,  458,  459,  -1,   -1,   -1,   -1,
    -1,   460,  -1,   -1,   461,  -1,   -1,   -1,   -1,   462,  -1,   -1,
    -1,   463,  -1,   -1,   -1,   464,  -1,   -1,   -1,   -1,   465,  -1,
    466,  467,  468,  469,  470,  -1,   -1,   -1,   471,  472,  473,  474,
    475,  -1,   -1,   -1,   476,  477,  -1,   478,  479,  480,  481,  482,
    483,  484,  485,  486,  487,  -1,   -1,   -1,   488,  489,  490,  491,
    492,  -1,   -1,   -1,   -1,   -1,   493,  -1,   -1,   494,  -1,   -1,
    -1,   -1,   495,  -1,   -1,   -1,   496,  -1,   -1,   -1,   497,  -1,
    -1,   -1,   -1,   498,  -1,   499,  500,  501,  502,  503,  -1,   -1,
    -1,   504,  505,  506,  507,  508,  -1,   -1,   509,  510,  511,  -1,
    512,  513,  514,  515,  -1,   516,  517,  518,  519,  520,  -1,   -1,
    -1,   521,  522,  523,  524,  525,  -1,   -1,   526,  -1,   -1,   527,
    -1,   -1,   528,  -1,   -1,   -1,   -1,   529,  -1,   -1,   -1,   530,
    -1,   -1,   -1,   531,  -1,   -1,   -1,   -1,   -1,   -1,   532,  533,
    534,  535,  536,  -1,   -1,   -1,   537,  538,  539,  540,  541,  -1,
    542,  543,  544,  545,  -1,   546,  547,  548,  -1,   -1,   549,  550,
    551,  552,  553,  -1,   -1,   -1,   554,  555,  556,  557,  558,  -1,
    -1,   559,  -1,   -1,   560,  -1,   -1,   561,  562,  -1,   -1,   -1,
    563,  -1,   -1,   -1,   -1,   -1,   -1,   -1,   564,  -1,   -1,   -1,
    -1,   -1,   -1,   565,  566,  567,  568,  569,  -1,   -1,   -1,   570,
    571,  572,  573,  574,  575,  576,  577,  578,  579,  -1,   580,  581,
    -1,   -1,   -1,   582,  583,  584,  585,  586,  -1,   -1,   -1,   587,
    588,  589,  590,  591,  -1,   -1,   592,  -1,   -1,   593,  -1,   -1,
    -1,   594,  -1,   -1,   -1,   595,  -1,   -1,   596,  -1,   -1,   -1,
    -1,   597,  -1,   -1,   -1,   -1,   -1,   -1,   598,  599,  600,  601,
    -1,   -1,   -1,   -1,   602,  603,  604,  605,  606,  607,  608,  609,
    610,  611,  -1,   612,  -1,   -1,   -1,   -1,   613,  614,  615,  616,
    -1,   -1,   -1,   -1,   617,  618,  619,  620,  -1,   -1,   -1,   621,
    -1,   -1,   622,  -1,   -1,   -1,   623,  -1,   -1,   -1,   624,  -1,
    -1,   625,  -1,   -1,   -1,   -1,   626,  -1,   -1,   -1,   -1,   -1,
    -1,   627,  628,  629,  -1,   -1,   -1,   -1,   -1,   630,  631,  632,
    633,  634,  635,  636,  637,  638,  639,  -1,   -1,   -1,   -1,   -1,
    -1,   640,  641,  642,  -1,   -1,   -1,   -1,   -1,   643,  644,  645,
    -1,   -1,   -1,   -1,   646,  -1,   -1,   647,  -1,   -1,   -1,   648,
    -1,   -1,   -1,   649,  -1,   -1,   650,  -1,   -1,   -1,   -1,   651,
    652,  -1,   -1,   653,  -1,   -1,   -1,   -1,   654,  655,  656,  -1,
    -1,   -1,   -1,   -1,   657,  658,  659,  -1,   -1,   -1,   -1,   -1,
    -1,   660,  661,  662,  663,  664,  665,  666,  667,  668,  669,  -1,
    -1,   -1,   -1,   -1,   670,  671,  672,  -1,   -1,   -1,   -1,   -1,
    673,  -1,   -1,   674,  -1,   -1,   -1,   -1,   675,  -1,   -1,   -1,
    676,  -1,   -1,   -1,   -1,   677,  -1,   -1,   678,  -1,   -1,   -1,
    679,  680,  681,  682,  -1,   -1,   -1,   -1,   683,  684,  685,  686,
    -1,   -1,   -1,   -1,   687,  -1,   688,  689,  690,  691,  692,  693,
    694,  695,  696,  697,  -1,   -1,   -1,   -1,   698,  699,  700,  701,
    -1,   -1,   -1,   -1,   -1,   702,  -1,   -1,   703,  -1,   -1,   -1,
    -1,   704,  -1,   -1,   -1,   705,  -1,   -1,   -1,   -1,   706,  -1,
    -1,   707,  -1,   -1,   708,  709,  710,  711,  712,  -1,   -1,   -1,
    713,  714,  715,  716,  717,  -1,   -1,   -1,   718,  719,  -1,   720,
    721,  722,  723,  724,  725,  726,  727,  728,  729,  -1,   -1,   -1,
    730,  731,  732,  733,  734,  -1,   -1,   -1,   -1,   -1,   735,  -1,
    -1,   736,  -1,   -1,   -1,   -1,   737,  -1,   -1,   -1,   738,  -1,
    739,  -1,   -1,   740,  -1,   -1,   741,  -1,   -1,   742,  743,  744,
    745,  746,  -1,   -1,   -1,   747,  748,  749,  750,  751,  -1,   -1,
    752,  753,  754,  -1,   755,  756,  757,  758,  -1,   759,  760,  761,
    762,  763,  -1,   -1,   -1,   764,  765,  766,  767,  768,  -1,   -1,
    769,  -1,   -1,   770,  -1,   -1,   771,  -1,   -1,   -1,   -1,   772,
    -1,   -1,   -1,   773,  -1,   774,  -1,   -1,   775,  -1,   -1,   776,
    -1,   -1,   777,  778,  779,  780,  781,  -1,   -1,   -1,   782,  783,
    784,  785,  786,  -1,   787,  788,  789,  790,  -1,   791,  792,  793,
    -1,   -1,   794,  795,  796,  797,  798,  -1,   -1,   -1,   799,  800,
    801,  802,  803,  -1,   -1,   804,  -1,   -1,   805,  -1,   -1,   806,
    807,  -1,   -1,   -1,   808,  -1,   -1,   -1,   -1,   -1,   809,  -1,
    -1,   810,  -1,   -1,   -1,   -1,   -1,   811,  812,  813,  814,  815,
    -1,   -1,   -1,   816,  817,  818,  819,  820,  821,  822,  823,  824,
    825,  -1,   826,  827,  -1,   -1,   -1,   828,  829,  830,  831,  832,
    -1,   -1,   -1,   833,  834,  835,  836,  837,  -1,   -1,   838,  -1,
    -1,   839,  -1,   -1,   -1,   840,  -1,   -1,   -1,   841,  -1,   -1,
    -1,   -1,   -1,   842,  -1,   -1,   843,  -1,   -1,   -1,   -1,   -1,
    844,  845,  846,  847,  -1,   -1,   -1,   -1,   848,  849,  850,  851,
    852,  853,  854,  855,  856,  857,  -1,   858,  -1,   -1,   -1,   -1,
    859,  860,  861,  862,  -1,   -1,   -1,   -1,   863,  864,  865,  866,
    -1,   -1,   -1,   867,  -1,   -1,   868,  -1,   -1,   -1,   869,  -1,
    -1,   -1,   870,  -1,   -1,   -1,   -1,   -1,   871,  -1,   -1,   872,
    -1,   -1,   -1,   -1,   -1,   873,  874,  875,  -1,   -1,   -1,   -1,
    -1,   876,  877,  878,  879,  880,  881,  882,  883,  884,  885,  -1,
    -1,   -1,   -1,   -1,   -1,   886,  887,  888,  -1,   -1,   -1,   -1,
    -1,   889,  890,  891,  -1,   -1,   -1,   -1,   892,  -1,   -1,   893,
    -1,   -1,   -1,   894,  -1,   -1,   -1,   895,  896,  -1,   -1,   -1,
    897,  -1,   -1,   -1,   898,  -1,   -1,   899,  -1,   -1,   -1,   -1,
    900,  901,  902,  -1,   -1,   -1,   -1,   -1,   903,  904,  905,  -1,
    -1,   -1,   -1,   -1,   -1,   906,  907,  908,  909,  910,  911,  912,
    913,  914,  915,  -1,   -1,   -1,   -1,   -1,   916,  917,  918,  -1,
    -1,   -1,   -1,   -1,   919,  -1,   -1,   920,  -1,   -1,   -1,   -1,
    -1,   921,  -1,   -1,   -1,   922,  -1,   -1,   -1,   923,  -1,   -1,
    924,  -1,   -1,   -1,   925,  926,  927,  928,  -1,   -1,   -1,   -1,
    929,  930,  931,  932,  -1,   -1,   -1,   -1,   933,  -1,   934,  935,
    936,  937,  938,  939,  940,  941,  942,  943,  -1,   -1,   -1,   -1,
    944,  945,  946,  947,  -1,   -1,   -1,   -1,   -1,   948,  -1,   -1,
    949,  -1,   -1,   -1,   -1,   -1,   950,  -1,   -1,   -1,   951,  -1,
    -1,   -1,   952,  -1,   -1,   953,  -1,   -1,   954,  955,  956,  957,
    958,  -1,   -1,   -1,   959,  960,  961,  962,  963,  -1,   -1,   -1,
    964,  965,  -1,   966,  967,  968,  969,  970,  971,  972,  973,  974,
    975,  -1,   -1,   -1,   976,  977,  978,  979,  980,  -1,   -1,   -1,
    -1,   -1,   981,  -1,   -1,   982,  -1,   -1,   -1,   -1,   -1,   983,
    -1,   -1,   -1,   984,  985,  -1,   -1,   986,  -1,   -1,   987,  -1,
    -1,   988,  989,  990,  991,  992,  -1,   -1,   -1,   993,  994,  995,
    996,  997,  -1,   -1,   998,  999,  1000, -1,   1001, 1002, 1003, 1004,
    -1,   1005, 1006, 1007, 1008, 1009, -1,   -1,   -1,   1010, 1011, 1012,
    1013, 1014, -1,   -1,   1015, -1,   -1,   1016, -1,   -1,   1017, -1,
    1018, -1,   -1,   -1,   1019, -1,   -1,   -1,   -1,   1020, -1,   -1,
    1021, -1,   -1,   1022, -1,   -1,   1023, 1024, 1025, 1026, 1027, -1,
    -1,   -1,   1028, 1029, 1030, 1031, 1032, -1,   1033, 1034, 1035, 1036,
    -1,   1037, 1038, 1039, -1,   -1,   1040, 1041, 1042, 1043, 1044, -1,
    -1,   -1,   1045, 1046, 1047, 1048, 1049, -1,   -1,   1050, -1,   -1,
    1051, -1,   -1,   1052, -1,   1053, -1,   -1,   -1,   1054, -1,   -1,
    -1,   -1,   1055, -1,   -1,   1056, -1,   -1,   -1,   -1,   -1,   1057,
    1058, 1059, 1060, 1061, -1,   -1,   -1,   1062, 1063, 1064, 1065, 1066,
    1067, 1068, 1069, 1070, 1071, -1,   1072, 1073, -1,   -1,   -1,   1074,
    1075, 1076, 1077, 1078, -1,   -1,   -1,   1079, 1080, 1081, 1082, 1083,
    -1,   -1,   1084, -1,   -1,   1085, -1,   -1,   -1,   -1,   1086, -1,
    -1,   -1,   1087, -1,   -1,   -1,   -1,   1088, -1,   -1,   1089, -1,
    -1,   -1,   -1,   -1,   1090, 1091, 1092, 1093, -1,   -1,   -1,   -1,
    1094, 1095, 1096, 1097, 1098, 1099, 1100, 1101, 1102, 1103, -1,   1104,
    -1,   -1,   -1,   -1,   1105, 1106, 1107, 1108, -1,   -1,   -1,   -1,
    1109, 1110, 1111, 1112, -1,   -1,   -1,   1113, -1,   -1,   1114, -1,
    -1,   -1,   -1,   1115, -1,   -1,   -1,   1116, -1,   -1,   -1,   -1,
    1117, -1,   -1,   1118, -1,   -1,   -1,   -1,   -1,   1119, 1120, 1121,
    -1,   -1,   -1,   -1,   -1,   1122, 1123, 1124, 1125, 1126, 1127, 1128,
    1129, 1130, 1131, -1,   -1,   -1,   -1,   -1,   -1,   1132, 1133, 1134,
    -1,   -1,   -1,   -1,   -1,   1135, 1136, 1137, -1,   -1,   -1,   -1,
    1138, -1,   -1,   1139, 1140, -1,   -1,   -1,   -1,   1141, -1,   -1,
    1142, -1,   -1,   -1,   1143, -1,   -1,   -1,   1144, -1,   -1,   1145,
    -1,   -1,   -1,   -1,   1146, 1147, 1148, -1,   -1,   -1,   -1,   -1,
    1149, 1150, 1151, -1,   -1,   -1,   -1,   -1,   -1,   1152, 1153, 1154,
    1155, 1156, 1157, 1158, 1159, 1160, 1161, -1,   -1,   -1,   -1,   -1,
    1162, 1163, 1164, -1,   -1,   -1,   -1,   -1,   -1,   1165, -1,   -1,
    -1,   -1,   1166, -1,   -1,   1167, -1,   -1,   -1,   1168, -1,   -1,
    -1,   1169, -1,   -1,   1170, -1,   -1,   -1,   1171, 1172, 1173, 1174,
    -1,   -1,   -1,   -1,   1175, 1176, 1177, 1178, -1,   -1,   -1,   -1,
    1179, -1,   1180, 1181, 1182, 1183, 1184, 1185, 1186, 1187, 1188, 1189,
    -1,   -1,   -1,   -1,   1190, 1191, 1192, 1193, -1,   -1,   -1,   -1,
    -1,   -1,   1194, -1,   -1,   -1,   -1,   1195, -1,   -1,   1196, -1,
    -1,   -1,   1197, -1,   -1,   -1,   1198, -1,   -1,   1199, -1,   -1,
    1200, 1201, 1202, 1203, 1204, -1,   -1,   -1,   1205, 1206, 1207, 1208,
    1209, -1,   -1,   -1,   1210, 1211, -1,   1212, 1213, 1214, 1215, 1216,
    1217, 1218, 1219, 1220, 1221, -1,   -1,   -1,   1222, 1223, 1224, 1225,
    1226, -1,   -1,   -1,   -1,   -1,   -1,   1227, -1,   -1,   -1,   -1,
    -1,   -1,   -1,   1228, -1,   -1,   -1,   1229, 1230, -1,   -1,   1231,
    -1,   -1,   1232, -1,   -1,   1233, 1234, 1235, 1236, 1237, -1,   -1,
    -1,   1238, 1239, 1240, 1241, 1242, -1,   -1,   1243, 1244, 1245, -1,
    1246, 1247, 1248, 1249, -1,   1250, 1251, 1252, 1253, 1254, -1,   -1,
    -1,   1255, 1256, 1257, 1258, 1259, -1,   -1,   -1,   -1,   -1,   -1,
    1260, -1,   -1,   -1,   1261, -1,   -1,   -1,   1262, -1,   -1,   -1,
    -1,   1263, -1,   -1,   1264, -1,   -1,   1265, -1,   -1,   1266, 1267,
    1268, 1269, 1270, -1,   -1,   -1,   1271, 1272, 1273, 1274, 1275, -1,
    1276, 1277, 1278, 1279, -1,   1280, 1281, 1282, -1,   -1,   1283, 1284,
    1285, 1286, 1287, -1,   -1,   -1,   1288, 1289, 1290, 1291, 1292, -1,
    1293, -1,   -1,   -1,   -1,   1294, -1,   -1,   -1,   1295, -1,   -1,
    -1,   1296, -1,   -1,   -1,   -1,   1297, -1,   -1,   1298, -1,   -1,
    -1,   -1,   -1,   1299, 1300, 1301, 1302, 1303, -1,   -1,   -1,   1304,
    1305, 1306, 1307, 1308, 1309, 1310, 1311, 1312, 1313, -1,   1314, 1315,
    -1,   -1,   -1,   1316, 1317, 1318, 1319, 1320, -1,   -1,   -1,   1321,
    1322, 1323, 1324, 1325, -1,   1326, -1,   -1,   -1,   -1,   1327, -1,
    -1,   -1,   1328, -1,   -1,   -1,   1329, -1,   -1,   -1,   -1,   1330,
    -1,   -1,   1331, -1,   -1,   -1,   -1,   -1,   1332, 1333, 1334, 1335,
    -1,   -1,   -1,   -1,   1336, 1337, 1338, 1339, 1340, 1341, 1342, 1343,
    1344, 1345, -1,   1346, -1,   -1,   -1,   -1,   1347, 1348, 1349, 1350,
    -1,   -1,   -1,   -1,   1351, 1352, 1353, 1354, -1,   -1,   1355, -1,
    -1,   -1,   -1,   1356, -1,   -1,   -1,   1357, -1,   -1,   -1,   1358,
    -1,   -1,   -1,   -1,   1359, -1,   -1,   1360, -1,   -1,   -1,   -1,
    -1,   1361, 1362, 1363, -1,   -1,   -1,   -1,   -1,   1364, 1365, 1366,
    1367, 1368, 1369, 1370, 1371, 1372, 1373, -1,   -1,   -1,   -1,   -1,
    -1,   1374, 1375, 1376, -1,   -1,   -1,   -1,   -1,   1377, 1378, 1379,
    1380, -1,   -1,   -1,   -1,   -1,   1381, -1,   1382, -1,   -1,   -1,
    -1,   1383, -1,   -1,   1384, -1,   -1,   -1,   1385, -1,   -1,   -1,
    1386, -1,   -1,   1387, -1,   -1,   -1,   -1,   1388, 1389, 1390, -1,
    -1,   -1,   -1,   -1,   1391, 1392, 1393, -1,   -1,   -1,   -1,   -1,
    -1,   1394, 1395, 1396, 1397, 1398, 1399, 1400, 1401, 1402, 1403, -1,
    -1,   -1,   -1,   -1,   -1,   1404, -1,   -1,   -1,   -1,   -1,   1405,
    -1,   1406, -1,   -1,   -1,   -1,   1407, -1,   -1,   1408, -1,   -1,
    -1,   1409, -1,   -1,   -1,   1410, -1,   -1,   1411, -1,   -1,   -1,
    1412, 1413, 1414, 1415, -1,   -1,   -1,   -1,   1416, 1417, 1418, 1419,
    -1,   -1,   -1,   -1,   1420, -1,   1421, 1422, 1423, 1424, 1425, 1426,
    1427, 1428, 1429, 1430, -1,   -1,   -1,   -1,   -1,   -1,   1431, -1,
    -1,   -1,   -1,   -1,   -1,   -1,   1432, -1,   -1,   -1,   -1,   1433,
    -1,   -1,   1434, -1,   -1,   -1,   1435, -1,   -1,   -1,   1436, -1,
    -1,   1437, -1,   -1,   1438, 1439, 1440, 1441, 1442, -1,   -1,   -1,
    1443, 1444, 1445, 1446, 1447, -1,   -1,   -1,   1448, 1449, -1,   1450,
    1451, 1452, 1453, 1454, 1455, 1456, 1457, 1458, 1459, -1,   -1,   -1,
    -1,   -1,   -1,   1460, -1,   -1,   -1,   -1,   -1,   -1,   -1,   1461,
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   1462, -1,   -1,   -1,   1463,
    1464, -1,   -1,   1465, -1,   -1,   1466, -1,   -1,   1467, 1468, 1469,
    1470, 1471, -1,   -1,   -1,   1472, 1473, 1474, 1475, 1476, -1,   -1,
    1477, 1478, 1479, -1,   1480, 1481, 1482, 1483, -1,   1484, 1485, 1486,
    1487, 1488, -1,   -1,   -1,   -1,   -1,   -1,   1489, -1,   -1,   -1,
    -1,   -1,   -1,   -1,   1490, -1,   -1,   -1,   1491, -1,   -1,   -1,
    1492, -1,   -1,   -1,   -1,   1493, -1,   -1,   1494, -1,   -1,   1495,
    -1,   -1,   1496, 1497, 1498, 1499, 1500, -1,   -1,   -1,   1501, 1502,
    1503, 1504, 1505, -1,   1506, 1507, 1508, 1509, -1,   1510, 1511, 1512,
    -1,   -1,   1513, 1514, 1515, 1516, 1517, -1,   -1,   -1,   -1,   -1,
    -1,   1518, -1,   -1,   1519, -1,   -1,   -1,   -1,   1520, -1,   -1,
    -1,   1521, -1,   -1,   -1,   1522, -1,   -1,   -1,   -1,   1523, -1,
    -1,   1524, -1,   -1,   -1,   -1,   -1,   1525, 1526, 1527, 1528, 1529,
    -1,   -1,   -1,   1530, 1531, 1532, 1533, 1534, 1535, 1536, 1537, 1538,
    1539, -1,   1540, 1541, -1,   -1,   -1,   1542, 1543, 1544, 1545, 1546,
    1547, -1,   -1,   -1,   -1,   -1,   1548, -1,   -1,   1549, -1,   -1,
    -1,   -1,   1550, -1,   -1,   -1,   1551, -1,   -1,   -1,   1552, -1,
    -1,   -1,   -1,   1553, -1,   -1,   1554, -1,   -1,   -1,   -1,   -1,
    1555, 1556, 1557, 1558, -1,   -1,   -1,   -1,   1559, 1560, 1561, 1562,
    1563, 1564, 1565, 1566, 1567, 1568, -1,   1569, -1,   -1,   -1,   -1,
    1570, 1571, 1572, 1573, -1,   1574, -1,   -1,   -1,   -1,   -1,   1575,
    -1,   -1,   1576, -1,   -1,   -1,   -1,   1577, -1,   -1,   -1,   1578,
    -1,   -1,   -1,   1579, -1,   -1,   -1,   -1,   1580, -1,   -1,   1581,
    -1,   -1,   -1,   -1,   -1,   1582, 1583, 1584, -1,   -1,   -1,   -1,
    -1,   1585, 1586, 1587, 1588, 1589, 1590, 1591, 1592, 1593, 1594, -1,
    -1,   -1,   -1,   -1,   -1,   1595, 1596, 1597, 1598, -1,   -1,   -1,
    -1,   -1,   -1,   1599, 1600, -1,   -1,   -1,   -1,   -1,   1601, -1,
    1602, -1,   -1,   -1,   -1,   1603, -1,   -1,   1604, -1,   -1,   -1,
    1605, -1,   -1,   -1,   1606, -1,   -1,   1607, -1,   -1,   -1,   -1,
    1608, 1609, 1610, -1,   -1,   -1,   -1,   -1,   1611, 1612, 1613, -1,
    -1,   -1,   -1,   -1,   -1,   1614, 1615, 1616, 1617, 1618, 1619, 1620,
    -1,   1621, -1,   -1,   -1,   -1,   -1,   -1,   -1,   1622, -1,   -1,
    -1,   -1,   -1,   1623, -1,   1624, -1,   -1,   -1,   -1,   1625, -1,
    -1,   1626, -1,   -1,   -1,   1627, -1,   -1,   -1,   1628, -1,   -1,
    1629, -1,   -1,   -1,   1630, 1631, 1632, 1633, -1,   -1,   -1,   -1,
    1634, 1635, 1636, 1637, -1,   -1,   -1,   -1,   1638, -1,   1639, 1640,
    1641, 1642, 1643, 1644, -1,   -1,   1645, -1,   -1,   -1,   -1,   -1,
    -1,   -1,   1646, -1,   -1,   -1,   -1,   -1,   -1,   -1,   1647, -1,
    -1,   -1,   -1,   1648, -1,   -1,   1649, -1,   -1,   -1,   1650, -1,
    -1,   -1,   1651, -1,   -1,   1652, -1,   -1,   1653, 1654, 1655, 1656,
    1657, -1,   -1,   -1,   1658, 1659, 1660, 1661, 1662, -1,   -1,   -1,
    1663, 1664, -1,   1665, 1666, 1667, 1668, 1669, -1,   -1,   -1,   1670,
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   1671, -1,   -1,   -1,   -1,
    -1,   -1,   -1,   1672, -1,   -1,   -1,   -1,   -1,   -1,   -1,   1673,
    -1,   -1,   -1,   1674, 1675, -1,   -1,   1676, -1,   -1,   1677, -1,
    -1,   1678, 1679, 1680, 1681, 1682, -1,   -1,   -1,   1683, 1684, 1685,
    1686, 1687, -1,   -1,   1688, 1689, 1690, -1,   1691, 1692, 1693, 1694,
    -1,   -1,   -1,   -1,   1695, -1,   -1,   -1,   -1,   -1,   -1,   -1,
    1696, -1,   -1,   -1,   -1,   -1,   -1,   -1,   1697, -1,   -1,   -1,
    1698, -1,   -1,   -1,   1699, -1,   -1,   -1,   -1,   1700, -1,   -1,
    1701, -1,   -1,   1702, -1,   -1,   1703, 1704, 1705, 1706, 1707, -1,
    -1,   -1,   1708, 1709, 1710, 1711, 1712, -1,   1713, 1714, 1715, 1716,
    -1,   1717, 1718, 1719, -1,   -1,   -1,   -1,   -1,   1720, -1,   -1,
    -1,   -1,   -1,   -1,   -1,   1721, -1,   -1,   1722, -1,   -1,   -1,
    -1,   1723, -1,   -1,   -1,   1724, -1,   -1,   -1,   1725, -1,   -1,
    -1,   -1,   1726, -1,   -1,   1727, -1,   -1,   -1,   -1,   -1,   1728,
    1729, 1730, 1731, 1732, -1,   -1,   -1,   1733, 1734, 1735, 1736, 1737,
    1738, 1739, 1740, 1741, 1742, -1,   1743, 1744, -1,   -1,   -1,   -1,
    -1,   -1,   1745, -1,   1746, -1,   -1,   -1,   -1,   -1,   1747, -1,
    -1,   1748, -1,   -1,   -1,   -1,   1749, -1,   -1,   -1,   1750, -1,
    -1,   -1,   1751, -1,   -1,   -1,   -1,   1752, -1,   -1,   1753, -1,
    -1,   -1,   -1,   -1,   1754, 1755, 1756, 1757, -1,   -1,   -1,   -1,
    1758, 1759, 1760, 1761, 1762, 1763, 1764, 1765, 1766, 1767, -1,   1768,
    1769, -1,   -1,   -1,   -1,   -1,   -1,   1770, -1,   1771, -1,   -1,
    -1,   -1,   -1,   1772, -1,   -1,   1773, -1,   -1,   -1,   -1,   1774,
    -1,   -1,   -1,   1775, -1,   -1,   -1,   1776, -1,   -1,   -1,   -1,
    1777, -1,   -1,   1778, -1,   -1,   -1,   -1,   -1,   1779, 1780, 1781,
    -1,   -1,   -1,   -1,   -1,   1782, 1783, 1784, 1785, 1786, 1787, 1788,
    1789, 1790, 1791, -1,   1792, 1793, 1794, 1795, 1796, 1797, -1,   -1,
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
    -1,   -1,   -1,   -1,   1798, 1799, 1800, 1801, 1802, 1803, 1804, 1805,
    1806, -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   1807, 1808, 1809, 1810, 1811,
    1812, 1813, 1814, 1815, -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   1816, 1817,
    1818, 1819, 1820, 1821, 1822, 1823, 1824, -1,   -1,   -1,   -1,   -1,
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
    -1,   1825, 1826, 1827, 1828, 1829, 1830, 1831, 1832, 1833, -1,   -1,
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
    -1,   -1,   -1,   -1,   1834, 1835, 1836, 1837, 1838, 1839, 1840, 1841,
    1842, -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   1843, 1844, 1845, 1846, 1847,
    1848, 1849, 1850, 1851, -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
    -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,   1852, 1853,
    1854, 1855, 1856, 1857};

constexpr int kNumPosEncodingChannels = 64;

const float kPosEncoding[64][kNumPosEncodingChannels] = {
    {-1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0,
     0.0,  0.0, 0.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0,
     0.0,  1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0,
     0.0,  1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0,
     0.0,  0.0, 1.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0},
    {1.0, -1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0,
     0.0, 0.0,  0.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0,
     0.0, 0.0,  1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0,
     0.0, 0.0,  1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0,
     0.0, 0.0,  0.0, 1.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0},
    {1.0, 1.0, -1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     0.0, 0.0, 0.0,  1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0,
     1.0, 0.0, 0.0,  1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0,
     0.0, 0.0, 0.0,  1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0,
     0.0, 0.0, 0.0,  0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0},
    {1.0, 1.0, 1.0, -1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 0.0, 0.0, 0.0,  1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 1.0, 0.0,
     0.0, 1.0, 0.0, 0.0,  1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0,
     1.0, 0.0, 0.0, 0.0,  1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0,
     0.0, 0.0, 0.0, 0.0,  0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0},
    {1.0, 1.0, 1.0, 1.0, -1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 0.0, 0.0, 0.0,  1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 1.0,
     0.0, 0.0, 1.0, 0.0, 0.0,  1.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0,
     0.0, 0.0, 0.0, 0.0, 0.0,  1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
     1.0, 0.0, 0.0, 0.0, 0.0,  0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0},
    {1.0, 1.0, 1.0, 1.0, 1.0, -1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 0.0, 0.0, 0.0,  1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0,
     1.0, 0.0, 0.0, 1.0, 0.0, 0.0,  0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0,
     0.0, 1.0, 0.0, 0.0, 0.0, 0.0,  1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
     0.0, 1.0, 0.0, 0.0, 0.0, 0.0,  0.0, 0.0, 0.0, 1.0, 0.0, 0.0},
    {1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0,
     1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0,  1.0, 1.0, 1.0, 1.0, 0.0, 0.0,
     0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0,  0.0, 1.0, 0.0, 0.0, 0.0, 1.0,
     0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0,  1.0, 0.0, 1.0, 0.0, 0.0, 0.0,
     0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0,  0.0, 0.0, 0.0, 1.0, 0.0},
    {1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0,
     1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0,  1.0, 1.0, 1.0, 0.0, 0.0,
     0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0,  0.0, 1.0, 0.0, 0.0, 0.0,
     1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0,  1.0, 0.0, 1.0, 0.0, 0.0,
     0.0, 0.0, 0.0, 1.0, 1.0, 0.0, 0.0, 0.0,  0.0, 0.0, 0.0, 1.0},
    {1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0,  0.0, 0.0, 1.0, 1.0,
     1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0,  1.0, 0.0, 0.0, 0.0,
     0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0,  1.0, 0.0, 0.0, 0.0,
     0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0,  0.0, 1.0, 0.0},
    {1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0,  0.0, 1.0, 1.0,
     1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0,  1.0, 0.0, 0.0,
     0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0,  1.0, 0.0, 0.0,
     0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0,  0.0, 1.0},
    {1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 1.0, 1.0, -1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0,  1.0, 1.0,
     1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0,  1.0, 0.0,
     0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0,  1.0, 0.0,
     0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0,  0.0},
    {0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 1.0, 1.0, 1.0, -1.0, 1.0,
     1.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0,  1.0,
     1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0,  1.0,
     0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0,  1.0,
     0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0},
    {0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0, -1.0,
     1.0, 1.0, 1.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0,
     1.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,
     1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0},
    {0.0,  0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     -1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0,
     0.0,  1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0,
     0.0,  0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0,
     0.0,  1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0},
    {0.0, 0.0,  0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, -1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0,
     0.0, 0.0,  1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0,
     0.0, 0.0,  0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0,
     0.0, 0.0,  1.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0},
    {0.0, 0.0, 0.0,  0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 0.0, 0.0,
     0.0, 0.0, 0.0,  1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0,
     1.0, 0.0, 0.0,  0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0,
     0.0, 0.0, 0.0,  1.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0},
    {1.0, 1.0, 1.0, 0.0,  0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 0.0, 0.0,
     0.0, 0.0, 0.0, -1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 0.0, 0.0, 0.0,  0.0, 0.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0,
     0.0, 1.0, 0.0, 0.0,  1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0,
     1.0, 0.0, 0.0, 0.0,  1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0},
    {1.0, 1.0, 1.0, 1.0, 0.0,  0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 0.0,
     0.0, 0.0, 0.0, 1.0, -1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 0.0, 0.0, 0.0,  0.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0,
     0.0, 0.0, 1.0, 0.0, 0.0,  1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0,
     0.0, 1.0, 0.0, 0.0, 0.0,  1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0},
    {1.0, 1.0, 1.0, 1.0, 1.0, 0.0,  0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     0.0, 0.0, 0.0, 1.0, 1.0, -1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 0.0, 0.0, 0.0,  1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0,
     0.0, 0.0, 0.0, 1.0, 0.0, 0.0,  1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0,
     0.0, 0.0, 1.0, 0.0, 0.0, 0.0,  1.0, 0.0, 0.0, 0.0, 0.0, 1.0},
    {0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0,  0.0, 0.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 0.0, 0.0, 1.0, 1.0, 1.0, -1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0,  1.0, 1.0, 1.0, 1.0, 1.0, 0.0,
     0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0,  1.0, 0.0, 0.0, 0.0, 0.0, 1.0,
     0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0,  1.0, 0.0, 0.0, 0.0, 0.0},
    {0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0,  0.0, 0.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0, -1.0, 1.0, 1.0, 1.0, 0.0, 0.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0,  1.0, 1.0, 1.0, 1.0, 1.0,
     0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0,  1.0, 1.0, 0.0, 0.0, 0.0,
     1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0,  1.0, 0.0, 0.0, 0.0},
    {0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0,  0.0, 0.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -1.0, 1.0, 1.0, 0.0, 0.0,
     0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0,  1.0, 1.0, 1.0, 1.0,
     1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0,  0.0, 1.0, 0.0, 0.0,
     0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0,  1.0, 0.0, 0.0},
    {0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0,  0.0, 0.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -1.0, 1.0, 0.0, 0.0,
     0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0,  1.0, 1.0, 1.0,
     1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0,  0.0, 1.0, 0.0,
     0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0,  1.0, 0.0},
    {0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0,  0.0, 0.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -1.0, 0.0, 0.0,
     0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0,  1.0, 1.0,
     1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0,  0.0, 1.0,
     0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0,  1.0},
    {1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 0.0,  0.0,
     0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0,  0.0,
     0.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0,  1.0,
     0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0},
    {0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 0.0,
     0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0,
     0.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0,
     1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0},
    {0.0,  0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     0.0,  0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 1.0, 1.0,
     -1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0,
     0.0,  1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0,
     0.0,  1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0},
    {1.0, 0.0,  0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 0.0,  0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 1.0, 1.0,
     1.0, -1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0,
     0.0, 0.0,  1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0,
     0.0, 0.0,  1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0},
    {0.0, 1.0, 0.0,  0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 0.0,  0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 1.0,
     1.0, 1.0, -1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     0.0, 0.0, 0.0,  1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0,
     1.0, 0.0, 0.0,  1.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0},
    {0.0, 0.0, 1.0, 0.0,  0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 0.0,  0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, -1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 0.0, 0.0, 0.0,  1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 1.0, 0.0,
     0.0, 1.0, 0.0, 0.0,  0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0},
    {0.0, 0.0, 0.0, 1.0, 0.0,  0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0,
     1.0, 1.0, 1.0, 0.0, 0.0,  0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, -1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0,
     1.0, 0.0, 0.0, 0.0, 0.0,  1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 1.0,
     0.0, 0.0, 1.0, 0.0, 0.0,  0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0},
    {0.0, 0.0, 0.0, 0.0, 1.0, 0.0,  0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0,
     1.0, 1.0, 1.0, 0.0, 0.0, 0.0,  0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0,
     1.0, 0.0, 0.0, 0.0, 0.0, 0.0,  1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0,
     1.0, 0.0, 0.0, 1.0, 0.0, 0.0,  0.0, 1.0, 0.0, 0.0, 0.0, 1.0},
    {1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0,  0.0, 1.0, 0.0, 0.0, 1.0, 0.0,
     0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 0.0,  0.0, 0.0, 0.0, 0.0, 1.0, 1.0,
     1.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0,  0.0, 0.0, 1.0, 1.0, 1.0, 0.0,
     0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0,  1.0, 0.0, 0.0, 0.0, 0.0},
    {0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0,  0.0, 1.0, 0.0, 0.0, 1.0,
     0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 0.0,  0.0, 0.0, 0.0, 1.0, 1.0,
     1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0,  0.0, 1.0, 1.0, 1.0, 1.0,
     0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0,  1.0, 0.0, 0.0, 0.0},
    {0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0,  0.0, 1.0, 0.0, 0.0,
     1.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0,  0.0, 0.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 1.0, 1.0, -1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0,  1.0, 1.0, 1.0, 1.0,
     1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0,  1.0, 0.0, 0.0},
    {0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 1.0, 0.0,  0.0, 1.0, 0.0,
     0.0, 1.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0,  0.0, 0.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 1.0, 1.0, 1.0, -1.0, 1.0, 1.0, 1.0,
     1.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0,  1.0, 1.0, 1.0,
     1.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0,  1.0, 0.0},
    {1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0,  0.0, 1.0,
     0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0,  0.0, 0.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0, -1.0, 1.0, 1.0,
     1.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0,  1.0, 1.0,
     1.0, 1.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0,  1.0},
    {0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0,  0.0,
     1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0,  0.0,
     0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -1.0, 1.0,
     1.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0,  1.0,
     1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0},
    {0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0,
     0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0,
     0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -1.0,
     1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0,
     1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0},
    {0.0,  0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0,
     0.0,  0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 0.0, 0.0,
     0.0,  0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0,
     0.0,  1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0},
    {1.0, 0.0,  0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0,
     0.0, 0.0,  0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0,
     1.0, 0.0,  0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0,
     0.0, -1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0,
     0.0, 0.0,  0.0, 0.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0},
    {0.0, 1.0, 0.0,  0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0,
     1.0, 0.0, 0.0,  0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 1.0,
     1.0, 1.0, 0.0,  0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0,
     0.0, 1.0, -1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     0.0, 0.0, 0.0,  0.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0},
    {0.0, 0.0, 1.0, 0.0,  0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0,
     0.0, 1.0, 0.0, 0.0,  0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 0.0,  0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0,
     0.0, 1.0, 1.0, -1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 0.0, 0.0, 0.0,  1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0},
    {0.0, 0.0, 0.0, 1.0, 0.0,  0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0,
     0.0, 0.0, 1.0, 1.0, 0.0,  0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 0.0,  0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0,
     0.0, 1.0, 1.0, 1.0, -1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 0.0, 0.0, 0.0,  1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0},
    {0.0, 0.0, 0.0, 0.0, 1.0, 0.0,  0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0,
     0.0, 0.0, 0.0, 0.0, 1.0, 0.0,  0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 0.0,  0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     0.0, 1.0, 1.0, 1.0, 1.0, -1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 0.0, 0.0, 0.0,  1.0, 1.0, 1.0, 1.0, 1.0, 0.0},
    {1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0,  0.0, 0.0, 1.0, 0.0, 0.0, 0.0,
     1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0,  0.0, 1.0, 0.0, 0.0, 0.0, 0.0,
     0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0,  0.0, 0.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0,  1.0, 1.0, 1.0, 1.0, 1.0},
    {0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0,  0.0, 0.0, 1.0, 0.0, 0.0,
     0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0,  0.0, 1.0, 0.0, 0.0, 0.0,
     0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0,  0.0, 0.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -1.0, 1.0, 0.0, 0.0, 0.0, 0.0,
     1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0,  1.0, 1.0, 1.0, 1.0},
    {0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0,  0.0, 0.0, 1.0, 0.0,
     0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0,  0.0, 1.0, 0.0, 0.0,
     0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0,  0.0, 0.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -1.0, 0.0, 0.0, 0.0, 0.0,
     0.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0,  1.0, 1.0, 1.0},
    {1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 1.0, 0.0,  0.0, 0.0, 0.0,
     1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0,  0.0, 1.0, 0.0,
     0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 0.0,  0.0, 0.0, 0.0,
     0.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, -1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0,  0.0, 0.0},
    {0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 1.0, 0.0,  0.0, 0.0,
     0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0,  0.0, 1.0,
     0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 0.0,  0.0, 0.0,
     0.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, -1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0,  0.0},
    {0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0,  0.0,
     0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0,  0.0,
     1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0,  0.0,
     0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 1.0, 1.0, -1.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0},
    {0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0,
     0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 1.0, 0.0,
     0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0,
     0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 1.0, 1.0, 1.0, -1.0,
     1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0},
    {0.0,  0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0,
     0.0,  0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0,
     0.0,  0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     0.0,  0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0,
     -1.0, 1.0, 1.0, 1.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0},
    {0.0, 0.0,  0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0,
     1.0, 0.0,  0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0,
     1.0, 0.0,  0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 0.0,  0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, -1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0},
    {1.0, 0.0, 0.0,  0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0,
     0.0, 1.0, 0.0,  0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0,
     0.0, 1.0, 0.0,  0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0,
     1.0, 0.0, 0.0,  0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, -1.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0},
    {0.0, 1.0, 0.0, 0.0,  0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0,
     0.0, 0.0, 1.0, 0.0,  0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0,
     0.0, 0.0, 1.0, 0.0,  0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0,
     1.0, 0.0, 0.0, 0.0,  0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 1.0, -1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0},
    {1.0, 0.0, 0.0, 0.0, 0.0,  0.0, 0.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0,
     0.0, 1.0, 0.0, 1.0, 0.0,  0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0,
     0.0, 0.0, 1.0, 0.0, 0.0,  0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0,
     0.0, 1.0, 1.0, 1.0, 0.0,  0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 0.0,
     0.0, 0.0, 0.0, 0.0, -1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0},
    {0.0, 1.0, 0.0, 0.0, 0.0, 0.0,  0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0,
     0.0, 0.0, 1.0, 0.0, 1.0, 0.0,  0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 1.0,
     0.0, 0.0, 0.0, 1.0, 0.0, 0.0,  0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0,
     0.0, 1.0, 1.0, 1.0, 1.0, 0.0,  0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0,
     0.0, 0.0, 0.0, 0.0, 1.0, -1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0},
    {0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0,  0.0, 0.0, 0.0, 1.0, 0.0, 0.0,
     0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0,  0.0, 0.0, 0.0, 1.0, 0.0, 0.0,
     1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0,  0.0, 1.0, 0.0, 0.0, 1.0, 0.0,
     0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0,  0.0, 0.0, 1.0, 1.0, 1.0, 1.0,
     1.0, 0.0, 0.0, 0.0, 1.0, 1.0, -1.0, 1.0, 1.0, 1.0, 1.0, 1.0},
    {0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0,  0.0, 0.0, 0.0, 1.0, 0.0,
     0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0,  0.0, 0.0, 0.0, 0.0, 0.0,
     0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 1.0, 0.0,  0.0, 1.0, 0.0, 0.0, 1.0,
     0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0,  0.0, 0.0, 1.0, 1.0, 1.0,
     1.0, 1.0, 0.0, 0.0, 1.0, 1.0, 1.0, -1.0, 1.0, 1.0, 1.0, 1.0},
    {0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0,  0.0, 0.0, 0.0, 1.0,
     0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0,  0.0, 0.0, 1.0, 0.0,
     0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0,  0.0, 1.0, 0.0, 0.0,
     1.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0,  0.0, 0.0, 1.0, 1.0,
     1.0, 1.0, 1.0, 0.0, 1.0, 1.0, 1.0, 1.0, -1.0, 1.0, 1.0, 1.0},
    {0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0,  0.0, 0.0, 0.0,
     1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0,  0.0, 0.0, 1.0,
     0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0,  0.0, 1.0, 0.0,
     0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 0.0,  0.0, 0.0, 1.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -1.0, 1.0, 1.0},
    {0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 1.0, 0.0, 0.0,  0.0, 0.0,
     0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0,  0.0, 0.0,
     1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0,  0.0, 1.0,
     0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 1.0, 0.0, 0.0,  0.0, 0.0,
     1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -1.0, 1.0},
    {1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 1.0, 0.0, 0.0, 0.0,
     0.0, 0.0, 1.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0,
     0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0,
     1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 0.0,
     0.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, -1.0}};

}  // namespace lczero
