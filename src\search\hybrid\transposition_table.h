/*
  This file is part of Leela Chess Zero.
  Copyright (C) 2024 The LCZero Authors

  Leela Chess is free software: you can redistribute it and/or modify
  it under the terms of the GNU General Public License as published by
  the Free Software Foundation, either version 3 of the License, or
  (at your option) any later version.

  Leela Chess is distributed in the hope that it will be useful,
  but WITHOUT ANY WARRANTY; without even the implied warranty of
  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
  GNU General Public License for more details.

  You should have received a copy of the GNU General Public License
  along with Lee<PERSON> Chess.  If not, see <http://www.gnu.org/licenses/>.
*/

#pragma once

#include <atomic>
#include <cstdint>
#include <memory>
#include <vector>

#include "chess/board.h"

namespace lczero {
namespace hybrid {

// Type of bound stored in TT entry
enum class BoundType : uint8_t {
  NONE = 0,
  EXACT = 1,
  LOWER = 2,  // Beta cutoff, score >= beta
  UPPER = 3   // Alpha cutoff, score <= alpha
};

// Transposition table entry
struct TTEntry {
  uint64_t hash;        // Full position hash for verification
  float score;          // Evaluation score
  float static_eval;    // Static evaluation (from NN)
  Move best_move;       // Best move found
  uint8_t depth;        // Search depth
  BoundType bound;      // Type of bound
  uint8_t generation;   // For replacement strategy
  
  TTEntry() : hash(0), score(0), static_eval(0), best_move(), 
              depth(0), bound(BoundType::NONE), generation(0) {}
};

// Cluster of TT entries for better cache performance
struct TTCluster {
  static constexpr size_t kClusterSize = 3;
  TTEntry entries[kClusterSize];
  std::atomic<uint64_t> lock{0};  // Spinlock for thread safety
};

class TranspositionTable {
 public:
  // size_mb: Size of the transposition table in megabytes
  explicit TranspositionTable(size_t size_mb);
  ~TranspositionTable() = default;
  
  // Store a position in the transposition table
  void Store(uint64_t hash, float score, float static_eval, Move best_move,
             int depth, BoundType bound);
  
  // Probe the transposition table
  // Returns nullptr if position not found
  const TTEntry* Probe(uint64_t hash) const;
  
  // Clear the transposition table
  void Clear();
  
  // Increment generation (called at start of each search)
  void NewSearch() { generation_++; }
  
  // Get fill rate (percentage of non-empty entries)
  float GetFillRate() const;
  
  // Adjust score for mate distance
  static float ScoreFromTT(float score, int ply);
  static float ScoreToTT(float score, int ply);

 private:
  // Get cluster index for a given hash
  size_t GetClusterIndex(uint64_t hash) const {
    return (hash >> 32) % num_clusters_;
  }
  
  // Lock/unlock cluster for thread-safe access
  void LockCluster(TTCluster& cluster) const;
  void UnlockCluster(TTCluster& cluster) const;
  
  // Find best entry to replace in cluster
  TTEntry* FindReplacement(TTCluster& cluster, uint64_t hash, int depth);

 private:
  std::unique_ptr<TTCluster[]> table_;
  size_t num_clusters_;
  std::atomic<uint8_t> generation_{0};
  
  // Constants for mate score adjustment
  static constexpr float kMateScore = 10000.0f;
  static constexpr float kMateThreshold = 9000.0f;
};

}  // namespace hybrid
}  // namespace lczero