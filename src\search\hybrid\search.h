/*
  This file is part of Leela Chess Zero.
  Copyright (C) 2024 The LCZero Authors

  Leela Chess is free software: you can redistribute it and/or modify
  it under the terms of the GNU General Public License as published by
  the Free Software Foundation, either version 3 of the License, or
  (at your option) any later version.

  Leela Chess is distributed in the hope that it will be useful,
  but WITHOUT ANY WARRANTY; without even the implied warranty of
  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
  GNU General Public License for more details.

  You should have received a copy of the GNU General Public License
  along with Lee<PERSON> Chess.  If not, see <http://www.gnu.org/licenses/>.

  Additional permission under GNU GPL version 3 section 7

  If you modify this Program, or any covered work, by linking or
  combining it with NVIDIA Corporation's libraries from the NVIDIA CUDA
  Toolkit and the NVIDIA CUDA Deep Neural Network library (or a
  modified version of those libraries), containing parts covered by the
  terms of the respective license agreement, the licensors of this
  Program grant you additional permission to convey the resulting work.
*/

#pragma once

#include <atomic>
#include <memory>
#include <thread>
#include <vector>

#include "chess/gamestate.h"
#include "chess/position.h"
#include "neural/backend.h"
#include "search/search.h"
#include "syzygy/syzygy.h"
#include "utils/optionsdict.h"

namespace lczero {
namespace hybrid {

// Forward declarations
class HybridNode;
class TranspositionTable;

// Represents a move with its evaluation
struct ScoredMove {
  Move move;
  float score;
  float policy;
  int depth;
};

// Hybrid search that combines alpha-beta with MCTS
class HybridSearch : public SearchBase {
 public:
  HybridSearch(UciResponder* responder, const OptionsDict* options);
  ~HybridSearch() override;

  // SearchBase interface implementation
  void NewGame() override;
  void SetPosition(const GameState& state) override;
  void StartSearch(const GoParams& params) override;
  void StartClock() override;
  void WaitSearch() override;
  void StopSearch() override;
  void AbortSearch() override;

 private:
  // Alpha-beta search with neural network evaluation
  float AlphaBeta(const Position& pos, float alpha, float beta, int depth,
                  int ply, std::vector<Move>* pv);
  
  // Minimax search at root position
  ScoredMove SearchRoot(const Position& pos, int depth);
  
  // Evaluate position using neural network
  float EvaluatePosition(const Position& pos);
  
  // Generate and order moves
  MoveList GenerateMoves(const Position& pos);
  std::vector<ScoredMove> OrderMoves(const Position& pos, const MoveList& moves);
  
  // MCTS fallback for deeper search
  void RunMCTS(const Position& pos, int iterations);
  
  // Worker thread function
  void SearchThread();
  
  // Check if time/node limits reached
  bool ShouldStop() const;
  
  // Update search info
  void SendInfo(int depth, float score, const std::vector<Move>& pv);

 private:
  // Configuration
  int alpha_beta_depth_;  // Max depth for alpha-beta
  int mcts_iterations_;   // MCTS iterations after alpha-beta
  bool use_transposition_table_;
  float contempt_;        // Contempt factor
  
  // Current search state
  GameState current_position_;
  std::atomic<bool> stop_{false};
  std::atomic<bool> pondering_{false};
  std::chrono::steady_clock::time_point start_time_;
  
  // Search limits
  std::optional<std::chrono::milliseconds> time_limit_;
  std::optional<int64_t> nodes_limit_;
  std::optional<int> depth_limit_;
  
  // Statistics
  std::atomic<int64_t> nodes_searched_{0};
  std::atomic<int> current_depth_{0};
  
  // Best move tracking
  Move best_move_;
  Move ponder_move_;
  float best_score_;
  
  // Transposition table
  std::unique_ptr<TranspositionTable> tt_;
  
  // Worker threads
  std::vector<std::thread> search_threads_;
  
  // Neural network cache
  struct NNCache {
    uint64_t hash;
    float value;
    std::vector<float> policy;
  };
  mutable std::mutex cache_mutex_;
  std::unordered_map<uint64_t, NNCache> nn_cache_;
  
  // Options dictionary
  const OptionsDict* options_;
};

// Factory for creating hybrid search
class HybridSearchFactory : public SearchFactory {
 public:
  std::string_view GetName() const override { return "hybrid"; }
  void PopulateParams(OptionsParser* options) const override;
  std::unique_ptr<SearchBase> CreateSearch(
      UciResponder* responder, const OptionsDict* options) const override;
};

}  // namespace hybrid
}  // namespace lczero