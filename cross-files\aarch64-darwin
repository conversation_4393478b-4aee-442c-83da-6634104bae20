
[host_machine]
system = 'darwin'
cpu_family = 'aarch64'
cpu = 'aarch64'
endian = 'little'

[properties]


[binaries]
c = 'clang'
cpp = 'clang++'
objc = 'clang'
objcpp = 'clang++'
ar = 'ar'
ld = 'ld'

[built-in options]
c_args = ['-arch', 'arm64']
cpp_args = ['-arch', 'arm64']
objc_args = ['-arch', 'arm64']
objcpp_args = ['-arch', 'arm64']
c_link_args = ['-arch', 'arm64']
cpp_link_args = ['-arch', 'arm64']
objc_link_args = ['-arch', 'arm64']
objcpp_link_args = ['-arch', 'arm64']
