
# Tested with Android NDK r27c, default toolchain
# Targeting API level 21

# Set the toolchain path on your environment
# export PATH="$HOME/.local/share/android-sdk/ndk-bundle/toolchains/llvm/prebuilt/linux-x86_64/bin:$PATH"

[host_machine]
system = 'android'
cpu_family = 'aarch64'
cpu = 'aarch64'
endian = 'little'

[properties]
cpp_link_args = ['-llog', '-static-libstdc++']

[binaries]
c = 'aarch64-linux-android21-clang'
cpp = 'aarch64-linux-android21-clang++'
ar = 'llvm-ar'
strip = 'llvm-strip'
ld = 'ld'
ranlib = 'llvm-ranlib'
as = 'aarch64-linux-android21-clang'
