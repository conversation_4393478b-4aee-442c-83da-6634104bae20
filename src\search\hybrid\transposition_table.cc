/*
  This file is part of Leela Chess Zero.
  Copyright (C) 2024 The LCZero Authors

  Leela Chess is free software: you can redistribute it and/or modify
  it under the terms of the GNU General Public License as published by
  the Free Software Foundation, either version 3 of the License, or
  (at your option) any later version.

  Leela Chess is distributed in the hope that it will be useful,
  but WITHOUT ANY WARRANTY; without even the implied warranty of
  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
  GNU General Public License for more details.

  You should have received a copy of the GNU General Public License
  along with Lee<PERSON> Chess.  If not, see <http://www.gnu.org/licenses/>.
*/

#include "search/hybrid/transposition_table.h"

#include <algorithm>
#include <cstring>
#include <limits>

namespace lczero {
namespace hybrid {

TranspositionTable::TranspositionTable(size_t size_mb) {
  // Calculate number of clusters
  size_t size_bytes = size_mb * 1024 * 1024;
  num_clusters_ = size_bytes / sizeof(TTCluster);
  
  // Allocate memory
  table_ = std::make_unique<TTCluster[]>(num_clusters_);
  
  // Initialize to zero
  Clear();
}

void TranspositionTable::Store(uint64_t hash, float score, float static_eval,
                              Move best_move, int depth, BoundType bound) {
  size_t cluster_idx = GetClusterIndex(hash);
  TTCluster& cluster = table_[cluster_idx];
  
  // Lock cluster
  LockCluster(cluster);
  
  // Find replacement entry
  TTEntry* entry = FindReplacement(cluster, hash, depth);
  
  // Store data
  entry->hash = hash;
  entry->score = ScoreToTT(score, 0);  // TODO: Pass ply for mate score adjustment
  entry->static_eval = static_eval;
  entry->best_move = best_move;
  entry->depth = static_cast<uint8_t>(std::min(depth, 255));
  entry->bound = bound;
  entry->generation = generation_;
  
  // Unlock cluster
  UnlockCluster(cluster);
}

const TTEntry* TranspositionTable::Probe(uint64_t hash) const {
  size_t cluster_idx = GetClusterIndex(hash);
  const TTCluster& cluster = table_[cluster_idx];
  
  // No locking needed for read-only probe
  // May get slightly stale data but that's acceptable
  
  // Search cluster for matching hash
  for (size_t i = 0; i < TTCluster::kClusterSize; ++i) {
    const TTEntry& entry = cluster.entries[i];
    if (entry.hash == hash && entry.bound != BoundType::NONE) {
      return &entry;
    }
  }
  
  return nullptr;
}

void TranspositionTable::Clear() {
  std::memset(table_.get(), 0, num_clusters_ * sizeof(TTCluster));
}

float TranspositionTable::GetFillRate() const {
  // Sample first 1000 clusters for efficiency
  size_t sample_size = std::min(num_clusters_, size_t(1000));
  size_t filled = 0;
  size_t total = sample_size * TTCluster::kClusterSize;
  
  for (size_t i = 0; i < sample_size; ++i) {
    const TTCluster& cluster = table_[i];
    for (size_t j = 0; j < TTCluster::kClusterSize; ++j) {
      if (cluster.entries[j].bound != BoundType::NONE) {
        filled++;
      }
    }
  }
  
  return static_cast<float>(filled) / static_cast<float>(total);
}

float TranspositionTable::ScoreFromTT(float score, int ply) {
  // Adjust mate scores based on ply
  if (score >= kMateThreshold) {
    return score - ply;
  } else if (score <= -kMateThreshold) {
    return score + ply;
  }
  return score;
}

float TranspositionTable::ScoreToTT(float score, int ply) {
  // Adjust mate scores based on ply
  if (score >= kMateThreshold) {
    return score + ply;
  } else if (score <= -kMateThreshold) {
    return score - ply;
  }
  return score;
}

void TranspositionTable::LockCluster(TTCluster& cluster) const {
  uint64_t expected = 0;
  while (!cluster.lock.compare_exchange_weak(expected, 1,
                                            std::memory_order_acquire,
                                            std::memory_order_relaxed)) {
    expected = 0;
  }
}

void TranspositionTable::UnlockCluster(TTCluster& cluster) const {
  cluster.lock.store(0, std::memory_order_release);
}

TTEntry* TranspositionTable::FindReplacement(TTCluster& cluster, uint64_t hash,
                                            int depth) {
  TTEntry* victim = nullptr;
  int victim_score = std::numeric_limits<int>::max();
  
  // First pass: look for exact hash match or empty entry
  for (size_t i = 0; i < TTCluster::kClusterSize; ++i) {
    TTEntry& entry = cluster.entries[i];
    
    // Exact hash match - always replace
    if (entry.hash == hash) {
      return &entry;
    }
    
    // Empty entry - use it
    if (entry.bound == BoundType::NONE) {
      return &entry;
    }
  }
  
  // Second pass: find least valuable entry to replace
  for (size_t i = 0; i < TTCluster::kClusterSize; ++i) {
    TTEntry& entry = cluster.entries[i];
    
    // Calculate replacement score (lower is more likely to be replaced)
    int score = 0;
    
    // Prefer replacing older generations
    if (entry.generation != generation_) {
      score -= 1000;
    }
    
    // Prefer keeping deeper searches
    score += entry.depth * 10;
    
    if (score < victim_score) {
      victim_score = score;
      victim = &entry;
    }
  }
  
  return victim;
}

}  // namespace hybrid
}  // namespace lczero