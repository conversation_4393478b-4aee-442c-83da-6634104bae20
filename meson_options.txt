option('openblas_include',
       type: 'array',
       value: ['/usr/include/openblas/'],
       description: 'Paths to openblas include directories')

option('opencl_include',
       type: 'string',
       value: '/usr/include/',
       description: 'Path to OpenCL include directory')

option('openblas_libdirs',
       type: 'array',
       value: ['/usr/lib/'],
       description: 'Paths to OpenBLAS libraries')

option('opencl_libdirs',
       type: 'array',
       value: ['/opt/cuda/lib64/', '/usr/local/cuda/lib64/'],
       description: 'Paths to OpenCL libraries')

option('cudnn_libdirs',
       type: 'array',
       value: ['/opt/cuda/lib64/', '/usr/local/cuda/lib64/', '/usr/lib/cuda/lib64/'],
       description: 'Paths to Cuda/cudnn libraries')

option('mkl_libdirs',
       type: 'array',
       value: ['/opt/intel/lib/intel64', '/opt/intel/mkl/lib/intel64', '/opt/intel/mkl/lib'],
       description: 'Paths to MKL libraries')

option('mkl_include',
       type: 'array',
       value: ['/opt/intel/mkl/include'],
       description: 'Paths to MKL libraries')

option('dnnl_dir',
       type: 'string',
       value: '/usr',
       description: 'Paths to DNNL install directory')

option('cudnn_include',
       type: 'array',
       value: ['/opt/cuda/include/', '/usr/local/cuda/include/', '/usr/lib/cuda/include/'],
       description: 'Paths to cudnn include directory')

option('build_backends',
       type: 'boolean',
       value: true,
       description: 'Build backends for NN computation')

option('blas',
       type: 'boolean',
       value: true,
       description: 'Enable BLAS backend')

option('ispc',
       type: 'boolean',
       value: true,
       description: 'use ispc')

option('ispc_native_only',
       type: 'boolean',
       value: true,
       description: 'use ispc and enable native arch only')

option('native_cuda',
       type: 'boolean',
       value: true,
       description: 'build cuda code for native arch only (if supported)')

option('cudnn',
       type: 'boolean',
       value: false,
       description: 'Enable cuDNN backend')

option('plain_cuda',
       type: 'boolean',
       value: true,
       description: 'Enable CUDA backend')

option('opencl',
       type: 'boolean',
       value: false,
       description: 'Enable OpenCL backend')

option('dx',
       type: 'boolean',
       value: false,
       description: 'Enable DirectX12 backend')

option('tensorflow',
       type: 'boolean',
       value: false,
       description: 'Enable TensorFlow backend')

option('onednn',
       type: 'boolean',
       value: false,
       description: 'Enable oneDNN backend')

option('openblas',
       type: 'boolean',
       value: true,
       description: 'Enable OpenBLAS support')

option('mkl',
       type: 'boolean',
       value: false,
       description: 'Enable MKL BLAS support')

option('dnnl',
       type: 'boolean',
       value: false,
       description: 'Enable DNNL BLAS support')

option('accelerate',
       type: 'boolean',
       value: true,
       description: 'Enable Accelerate BLAS support')

option('metal',
       type: 'feature',
       value: 'auto',
       description: 'Enable Metal backend')

option('malloc',
       type : 'string',
       value: '',
       description: 'Use alternative memory allocator, e.g. tcmalloc/jemalloc')

option('mimalloc_libdir',
       type : 'string',
       value: '',
       description: 'Library directory for malloc=mimalloc')

option('popcnt',
       type: 'boolean',
       value: true,
       description: 'Use the popcnt instruction')

option('f16c',
       type: 'boolean',
       value: true,
       description: 'Use natice fp16 conversion instructions')

option('pext',
       type: 'boolean',
       value: false,
       description: 'Use the pext instruction')

option('neon',
       type: 'boolean',
       value: true,
       description: 'Use neon instructions on arm processors')

option('gtest',
       type: 'boolean',
       value: true,
       description: 'Build gtest tests')

option('embed',
       type: 'boolean',
       value: false,
       description: 'Use embedded net by default')

option('nvcc_ccbin',
       type: 'string',
       value: '',
       description: 'Override C++ compiler used by cuda nvcc')

option('python_bindings',
       type: 'boolean',
       value: false,
       description: 'Build Python bindings for the python to bind.')

option('cc_cuda',
       type: 'string',
       value: '',
       description: 'Build for a specific cuda CC, e.g. -Dcc_cuda=35 for CC 3.5')

option('onnx_libdir',
       type: 'string',
       value: '',
       description: 'Paths to ONNX runtime libraries')

option('onnx_include',
       type: 'string',
       value: '',
       description: 'Paths to ONNX runtime includes')

option('xla',
       type: 'boolean',
       value: false,
       description: 'Enable XLA backend')

option('sycl',
       type: 'combo',
	   choices : ['off', 'l0', 'amd', 'nvidia'],
       value: 'off',
       description: 'Enable SYCL backend')

option('lc0',
       type: 'boolean',
       value: true,
       description: 'Build Lc0')

option('rescorer',
       type: 'boolean',
       value: false,
       description: 'Build rescorer')

option('default_search',
       type: 'string',
       value: '',
       description: 'Default search algorithm to use, e.g. -Ddefault_search=classic')

option('dag_classic',
       type: 'boolean',
       value: true,
       description: 'Enable dag-classic search algorithm')
