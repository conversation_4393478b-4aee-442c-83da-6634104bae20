[build-system]
requires = ["meson-python"]
build-backend = "mesonpy"

[project]
name = "lczero_bindings"
version = "0.1.0"
description = "Leela Chess Zero Python bindings"
authors = [{ name = "The LCZero Authors" }]
license = {file = "COPYING"}
readme = "README.md"
requires-python = ">=3.7"
classifiers = [
    "Programming Language :: Python :: 3",
    "Topic :: Games/Entertainment :: Board Games",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Environment :: GPU"
]

[project.urls]
homepage = "https://github.com/LeelaChessZero/lc0"

[tool.meson-python.args]
dist = []
setup = ["-Dpython_bindings=true"]
compile = []
install = []