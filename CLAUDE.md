# Lc0 Build Instructions and Hybrid Search Implementation

## Hybrid Search Implementation
Added a new hybrid search algorithm that combines alpha-beta pruning (like Stockfish) with MCTS. Files created:
- `src/search/hybrid/search.h` - Main search algorithm header
- `src/search/hybrid/search.cc` - Implementation with alpha-beta pruning
- `src/search/hybrid/transposition_table.h` - Transposition table for caching
- `src/search/hybrid/transposition_table.cc` - TT implementation
- `src/search/hybrid/wrapper.cc` - Registration with search factory
- `src/search/hybrid/README.md` - Documentation
- Modified `meson.build` to include hybrid search files

## Build Requirements
- C++20 compatible compiler (g++ 13+ or clang 15+)
- Meson build system: `pip install meson ninja`
- Protocol Buffers: `sudo apt-get install protobuf-compiler`
- Optional backends: CUDA, OpenCL, BLAS, etc.

## Build Instructions
1. **Setup build directory:**
   ```bash
   cd /mnt/c/Users/<USER>/lc0
   meson setup build --buildtype=release
   ```

2. **Compile:**
   ```bash
   meson compile -C build
   ```

3. **Alternative using build.sh:**
   ```bash
   ./build.sh
   ```

## Using Hybrid Search
Once built, the hybrid search can be selected via UCI:
```
setoption name SearchType value hybrid
```

## Hybrid Search Options
- `HybridAlphaBetaDepth` (default: 5) - Max depth for alpha-beta phase
- `HybridMCTSIterations` (default: 800) - MCTS iterations after alpha-beta
- `HybridUseTranspositionTable` (default: true) - Enable TT
- `HybridTTSizeMB` (default: 128) - Transposition table size in MB
- `HybridContempt` (default: 0) - Draw contempt factor

## Key Features
- Alpha-beta pruning for tactical search (1-5 plies)
- Neural network evaluation at leaf nodes
- Transposition table with proper bound types
- Move ordering using NN policy + classical heuristics
- Optional MCTS phase for deeper strategic analysis

## Notes
- The code requires C++20 due to newer language features in Lc0
- If meson is installed via pip, you may need to open a new terminal or run: `export PATH=$PATH:~/.local/bin`
- For WSL users: ensure line endings are LF, not CRLF

## Current Build Status
The hybrid search implementation is partially complete but needs API updates:

### Issues to Fix:
1. **GameState API**: Use `GetPositions()` instead of `GetPosition()`
2. **Position API**: 
   - No `ApplyMove()` method - need to use `Position(Position, Move)` constructor
   - Use `GetBoard()` for board access
   - Check methods need to be accessed through board
3. **Backend API**: No `RunBlocking()` - need to use computation graph
4. **UCI Output**: Need to use `BestMoveInfo` struct instead of direct move output
5. **Encoder**: Use proper namespace for `EncodePosition`
6. **Move string**: `Move::ToString()` requires chess960 parameter

### Next Steps:
1. Study existing search implementations more carefully
2. Update API calls to match current Lc0 codebase
3. Consider starting with simpler integration before full alpha-beta

The foundation is in place with:
- Transposition table implementation (compiles successfully)
- Search registration framework
- Basic alpha-beta structure
- Move ordering logic