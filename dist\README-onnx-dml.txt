Lc0

Lc0 is a UCI-compliant chess engine designed to play chess via
neural network, specifically those of the LeelaChessZero project
(https://lczero.org).

To run this version you will most likely need a very recent DirectML dll,
which you can get by running the included `install.cmd` script. Alternatively,
you can download the currently latest nuget installer package from
<https://www.nuget.org/api/v2/package/Microsoft.AI.DirectML/1.10.0>.
If you don't know how to use nuget installer packages, you can change the
extension to .zip and open it as a normal zip file, the dll you need is
`/bin/x64-win/DirectML.dll`.

License

Leela Chess is free software: you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation, either version 3 of the License, or
(at your option) any later version.

Leela Chess is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with <PERSON><PERSON>.  If not, see <http://www.gnu.org/licenses/>.

Additional permission under GNU GPL version 3 section 7

If you modify this Program, or any covered work, by linking or
combining it with NVIDIA Corporation's libraries from the NVIDIA CUDA
Toolkit and the NVIDIA CUDA Deep Neural Network library (or a
modified version of those libraries), containing parts covered by the
terms of the respective license agreement, the licensors of this
Program grant you additional permission to convey the resulting work.

