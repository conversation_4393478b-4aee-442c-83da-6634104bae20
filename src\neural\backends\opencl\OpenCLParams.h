/*
  Originally from the Leela Zero project.
  Copyright (C) 2017 <PERSON><PERSON><PERSON><PERSON>

  This file is part of Leela Chess Zero.
  Copyright (C) 2018 The LCZero Authors

  Leela Chess is free software: you can redistribute it and/or modify
  it under the terms of the GNU General Public License as published by
  the Free Software Foundation, either version 3 of the License, or
  (at your option) any later version.

  Leela Chess is distributed in the hope that it will be useful,
  but WITHOUT ANY WARRANTY; without even the implied warranty of
  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
  GNU General Public License for more details.

  You should have received a copy of the GNU General Public License
  along with Leela Chess.  If not, see <http://www.gnu.org/licenses/>.
*/

#pragma once

struct OpenCLParams {
  int gpuId = -1;

  bool tune_only = false;
  bool force_tune = false;
  bool tune_exhaustive = false;
  int tune_batch_size = 1;
  std::string tuner_file;
};
