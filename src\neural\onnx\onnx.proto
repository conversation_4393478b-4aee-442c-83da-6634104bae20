/*
  This file is part of Leela Chess Zero.
  Copyright (C) 2021 The LCZero Authors

  Leela Chess is free software: you can redistribute it and/or modify
  it under the terms of the GNU General Public License as published by
  the Free Software Foundation, either version 3 of the License, or
  (at your option) any later version.

  Leela Chess is distributed in the hope that it will be useful,
  but WITHOUT ANY WARRANTY; without even the implied warranty of
  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
  GNU General Public License for more details.

  You should have received a copy of the GNU General Public License
  along with <PERSON><PERSON>.  If not, see <http://www.gnu.org/licenses/>.

  Additional permission under GNU GPL version 3 section 7

  If you modify this Program, or any covered work, by linking or
  combining it with NVIDIA Corporation's libraries from the NVIDIA CUDA
  Toolkit and the NVIDIA CUDA Deep Neural Network library (or a
  modified version of those libraries), containing parts covered by the
  terms of the respective license agreement, the licensors of this
  Program grant you additional permission to convey the resulting work.
*/

syntax = "proto2";

package pblczero;

message TensorProto {
  enum DataType {
    UNDEFINED = 0;
    // Basic types.
    FLOAT = 1;   // float
    UINT8 = 2;   // uint8_t
    INT8 = 3;    // int8_t
    UINT16 = 4;  // uint16_t
    INT16 = 5;   // int16_t
    INT32 = 6;   // int32_t
    INT64 = 7;   // int64_t
    STRING = 8;  // string
    BOOL = 9;    // bool
    FLOAT16 = 10;
    DOUBLE = 11;
    UINT32 = 12;
    UINT64 = 13;
    COMPLEX64 = 14;
    COMPLEX128 = 15;
    BFLOAT16 = 16;
    FLOAT8E4M3FN = 17;
    FLOAT8E4M3FNUZ = 18;
    FLOAT8E5M2 = 19;
    FLOAT8E5M2FNUZ = 20;
  }
  repeated int64 dims = 1;
  optional DataType data_type = 2;
  optional string name = 8;
  optional bytes raw_data = 9;
  optional string doc_string = 12;
  repeated StringStringEntryProto external_data = 13;
  enum DataLocation {
    DEFAULT = 0;
    EXTERNAL = 1;
  }
  optional DataLocation data_location = 14;
}

message StringStringEntryProto {
  optional string key = 1;
  optional string value = 2;
}

message AttributeProto {
  enum AttributeType {
    UNDEFINED = 0;
    FLOAT = 1;
    INT = 2;
    STRING = 3;
    TENSOR = 4;
    // GRAPH = 5;

    FLOATS = 6;
    INTS = 7;
    STRINGS = 8;
    TENSORS = 9;
    // GRAPHS = 10;
  }  
  optional string name = 1;
  optional string doc_string = 13;

  // Exactly ONE of the following fields must be present
  optional float f = 2;               // float
  optional int64 i = 3;               // int
  optional bytes s = 4;               // UTF-8 string
  optional TensorProto t = 5;         // tensor value
  // optional GraphProto g = 6;          // graph

  repeated float floats = 7;          // list of floats
  repeated int64 ints = 8;            // list of ints
  repeated bytes strings = 9;         // list of UTF-8 strings
  repeated TensorProto tensors = 10;  // list of tensors
  // repeated GraphProto graphs = 11;    // list of graph

  optional AttributeType type = 20;
}

message NodeProto {
  repeated string input = 1;  
  repeated string output = 2; 
  optional string name = 3;   
  optional string op_type = 4;  
  repeated AttributeProto attribute = 5;
  optional string doc_string = 6;
  optional string domain = 7;
}

message TensorShapeProto {
  message Dimension {
    optional int64 dim_value = 1;
    optional string dim_param = 2;
  }
  repeated Dimension dim = 1;
}

message TypeProto {
  message Tensor {
    optional TensorProto.DataType elem_type = 1;
    optional TensorShapeProto shape = 2;
  }
  optional Tensor tensor_type = 1;
}

message ValueInfoProto {
  optional string name = 1;
  optional TypeProto type = 2;
  optional string doc_string = 3;
}

message GraphProto {
  repeated NodeProto node = 1;
  optional string name = 2;
  repeated TensorProto initializer = 5;
  optional string doc_string = 10;
  repeated ValueInfoProto input = 11;
  repeated ValueInfoProto output = 12;
}

message OperatorSetIdProto {
  optional string domain = 1;
  optional int64 version = 2;
}

// The subset of Onnx protos that are used in lczero.
message ModelProto {  
  optional int64 ir_version = 1;
  optional string producer_name = 2;
  optional string producer_version = 3;
  optional string domain = 4; // 'org.lczero.models.*'
  optional int64 model_version = 5;
  optional string doc_string = 6;
  optional GraphProto graph = 7;
  repeated OperatorSetIdProto opset_import = 8;
}
